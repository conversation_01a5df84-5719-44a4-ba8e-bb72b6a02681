import { sensorSchema, type TSensorSchema } from './sensor';
import { connectionChannelSchema, type TConnectionChannelSchema } from './connection-channel';
import { inputVariableSchema, type TInputVariableSchema } from './input-variable';
import { targetSchema, type TTargetSchema } from './target';
import { formulaSchema, type TFormulaSchema } from './formula';
import { functionLibrarySchema, type TFunctionLibrarySchema } from './function-library';
import { outputVariableSchema, type TOutputVariableSchema } from './output-variable';

import { statWidgetPropsSchema, type TStatWidgetProps } from './stat-widget-props';
import { lineWidgetPropsSchema, type TLineWidgetProps } from './line-widget-props';
import { barWidgetPropsSchema, type TBarWidgetProps } from './bar-widget-props';

import { ZodSchema } from 'zod';

export type EntityType = 'sensor' | 'connection-channel' | 'input-variable' | 'output-variable' | 'target' | 'formula' | 'function-library';
export type WidgetType = 'stat' | 'bar' | 'line' | 'table' | 'scatter' | 'double-side-bar';

export const schemaMap: Record<string, ZodSchema> = {
  sensor: sensorSchema,
  'connection-channel': connectionChannelSchema,
  'input-variable': inputVariableSchema,
  'output-variable': outputVariableSchema,
  target: targetSchema,
  formula: formulaSchema,
  'function-library': functionLibrarySchema,

  stat: statWidgetPropsSchema,
  line: lineWidgetPropsSchema,
  bar: barWidgetPropsSchema,
}

export type TEntitySchemaUnion = TSensorSchema
| TConnectionChannelSchema
| TInputVariableSchema
| TTargetSchema
| TFormulaSchema
| TFunctionLibrarySchema
| TOutputVariableSchema;

export type TWidgetPropsUnion = TStatWidgetProps
| TLineWidgetProps
| TBarWidgetProps;

export type TWidgetBase = {
  x: number;
  y: number;
  w: number;
  h: number;
  type: string;
  id: string;
}

