import React from 'react';
import { NodeProps, useReactFlow } from '@xyflow/react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormItem, FormControl, FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectItem, SelectContent } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ConnectionChannelNode } from './node'
import { connectionChannelTypeValues } from '@/types/connection-channel';
import { connectionChannelSchema, TConnectionChannelSchema } from '@/types/connection-channel';

export type ConnectionChannelFormProps = {
  setFormOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ConnectionChannelForm: React.FC<NodeProps<ConnectionChannelNode> & ConnectionChannelFormProps> = ({
  id, data, setFormOpen
}) => {
  const {setNodes} = useReactFlow();

  const form = useForm<Partial<TConnectionChannelSchema>>({
    resolver: zodResolver(connectionChannelSchema),
    defaultValues: {
      ...{
        sn: '',
        name: '连接通道 1',
        type: '整形',
        varname: 'ch1',
        sampleMode: '',
        sampleRate: 0,
        pendingSample: 0,
        deviceId: '',
        deviceSN: '',
        ai: 0,
        sensorId: '',
        sensorName: '',
      },
      ...data,
    }
  });

  const onSubmit = (data: Partial<TConnectionChannelSchema>) => {
    setNodes((nodes) => {
      return nodes.map((node) => {
        if (node.id === id) { return { ...node, data } }
        return node
      })
    })

    setFormOpen(false);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-2 p-2"
      >
        <FormField
          control={form.control}
          name="name"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>名称</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>

              <FormMessage className='text-right'>
                {
                  form.formState.errors.name ? form.formState.errors.name.message : ''
                }
              </FormMessage>

            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>类型</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger className='col-span-3 w-full'>
                      <SelectValue placeholder="选择数据类型" />
                    </SelectTrigger>

                    <SelectContent>
                      { connectionChannelTypeValues.map((type) => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      )) }
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.type ? form.formState.errors.type.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="varname"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>变量名</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>

              <FormMessage className='text-right'>
                {
                  form.formState.errors.varname ? form.formState.errors.varname.message : ''
                }
              </FormMessage>

            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="deviceSN"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>设备SN</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'  disabled={ true}/>
                </FormControl>
              </div>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sensorName"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>传感器</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'  disabled={ true}/>
                </FormControl>
              </div>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sampleMode"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>采样模式</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'  disabled={ true}/>
                </FormControl>
              </div>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sampleRate"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>采样率</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'  disabled={ true}/>
                </FormControl>
              </div>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="pendingSample"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>待读取采样数</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>
            </FormItem>
          )}
        />
        <Button type="submit" className='mt-4 cursor-pointer' disabled={form.formState.isSubmitting} >提交</Button>
      </form>
    </Form>
  );
};
