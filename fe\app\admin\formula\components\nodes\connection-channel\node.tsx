import React, { useState } from 'react';

import { type Node, type NodeProps, Position } from '@xyflow/react';

import {
  BaseNode,
  BaseNodeHeader,
  BaseNodeHeaderTitle,
} from '@/components/base-node';
import { LabeledHandle } from '@/components/labeled-handle';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ConnectionChannelIcon } from '@/components/icons'

import { Trash2, Pencil } from 'lucide-react';

import { ConfirmDialog } from '@/components/confirm-dialog';

import { TConnectionChannelSchema } from '@/types/connection-channel';
import { ConnectionChannelForm } from './form';
import { gradientStyle, cn, textColorStyle, defaultHandleStyle } from '@/lib/utils';
import { nodeColors } from '../../constants';

import { useAppStore } from '@/app/admin/store';
export type ConnectionChannelNode = Node<TConnectionChannelSchema>;

export function ConnectionChannelNode(node: NodeProps<ConnectionChannelNode>) {
  const { id, data } = node;
  const [onDeleteConfirmOpen, setOnDeleteConfirmOpen] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const { deleteNode } = useAppStore((state) => ({
    deleteNode: state.deleteNode,
  }))
  const [handleStyle, setHandleStyle] = useState<React.CSSProperties>({})

  return (
    <div className="group">
      <div className="flex p-3 space-x-2 justify-center items-center bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Trash2 onClick={() => { setOnDeleteConfirmOpen(true) }}/>
        <ConfirmDialog
          isOpen={onDeleteConfirmOpen}
          title="删除连接通道"
          description="确定要删除此连接通道吗？"
          onConfirm={() => { deleteNode(id)} }
          onClose={() => { setOnDeleteConfirmOpen(false) } }
        />
        <Pencil />
      </div>

      <BaseNode onClick={() => setShowForm(true)}
        onMouseEnter={() => {
          setHandleStyle(defaultHandleStyle())
        }}
        onMouseLeave={() => {
          setHandleStyle({})
        }}>
        <BaseNodeHeader className={cn("min-w-[240px] items-center justify-between py-2 rounded-md")}
          style={ gradientStyle(nodeColors['connection-channel'])  }
        >
          <LabeledHandle title=""
            id={`source-device`}
            type="source"
            position={Position.Left}
            style={handleStyle}
          />

          <ConnectionChannelIcon
            className={cn("w-4 h-4")}
            style={textColorStyle(nodeColors['connection-channel'])}
          />
          <BaseNodeHeaderTitle> { data.name }</BaseNodeHeaderTitle>

          <LabeledHandle title=""
            id={`source-formula`}
            type="source"
            position={Position.Right}
            style={handleStyle}
          />

        </BaseNodeHeader>
      </BaseNode>

      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className='flex flex-row items-center'>
              <ConnectionChannelIcon className="inline-block mr-2 w-4 h-4 text-primary" />
              修改连接通道
            </DialogTitle>
          </DialogHeader>

          <ConnectionChannelForm {...node} setFormOpen={setShowForm}/>
        </DialogContent>
      </Dialog>
    </div>
  );
}
