import React from 'react';
import { HexColorPicker } from 'react-colorful';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

export function ColorPicker({ color, onChange }: { color: string, onChange: (x: string) => void }) {

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" style={{ backgroundColor: color }}>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <HexColorPicker color={color} onChange={onChange} />
      </PopoverContent>
    </Popover>
  );
}

