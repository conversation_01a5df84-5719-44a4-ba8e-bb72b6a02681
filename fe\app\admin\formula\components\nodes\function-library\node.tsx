import React, { useState } from 'react';
import { type Node, type NodeProps, Position } from '@xyflow/react';

import {
  BaseNode,
  BaseNodeContent,
  BaseNodeFooter,
  BaseNodeHeader,
  BaseNodeHeaderTitle,
} from '@/components/base-node';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LabeledHandle } from '@/components/labeled-handle';
import Editor from '@monaco-editor/react';

import { ConfirmDialog } from '@/components/confirm-dialog';
import { Trash2, Pencil } from 'lucide-react';
import { FunctionLibraryIcon } from '@/components/icons';
import { FunctionLibraryForm } from './form';
import { TFunctionLibrarySchema } from '@/types/function-library';
import {  gradientStyle, cn, textColorStyle, defaultHandleStyle } from '@/lib/utils';
import { nodeColors } from '../../constants';
import { useAppStore } from '@/app/admin/store';

export type FunctionLibraryNode = Node<TFunctionLibrarySchema>;

export function FunctionLibraryNode(node: NodeProps<FunctionLibraryNode>) {
  const { id, data } = node;
  const [onDeleteConfirmOpen, setOnDeleteConfirmOpen] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const { deleteNode } = useAppStore((state) => ({
    deleteNode: state.deleteNode,
  }))
  const [handleStyle, setHandleStyle] = useState<React.CSSProperties>({})

  return (
    <div className="group">
      <div className="flex p-3 space-x-2 justify-center items-center bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Trash2 onClick={() => { setOnDeleteConfirmOpen(true) }} className='w-4 h-4'/>
        <ConfirmDialog
          isOpen={onDeleteConfirmOpen}
          onClose={() => setOnDeleteConfirmOpen(false)}
          title="删除自定义函数库"
          description="确定要删除此自定义函数库？"
          onConfirm={() => { deleteNode(id) }}
        />

        <Pencil className='w-4 h-4'/>
      </div>

      <BaseNode onClick={() => setShowForm(true)}
        onMouseEnter={() => {
          setHandleStyle(defaultHandleStyle())
        }}
        onMouseLeave={() => {
          setHandleStyle({})
        }}>
        <BaseNodeHeader className={cn("min-w-[240px] items-center justify-between py-2 rounded-md")}
          style={ gradientStyle(nodeColors['function-library'])  }
        >
          <FunctionLibraryIcon className={cn("w-4 h-4") }
            style={textColorStyle(nodeColors['function-library'])} />
          <BaseNodeHeaderTitle>{ data.name }</BaseNodeHeaderTitle>
          <LabeledHandle title="" type="source" position={Position.Right} 
            style={handleStyle} />
        </BaseNodeHeader>

        <BaseNodeContent className="p-2 relative">
          <Editor
            height="300px"
            defaultLanguage="python"
            value={data.code}
            options={{
              minimap: { enabled: false },
              fontSize: 14,
              wordWrap: "on",
              lineNumbers: "off",
              readOnly: true,
            }}
          />
        </BaseNodeContent>

        <BaseNodeFooter className="bg-gray-100 px-0 py-1 w-full rounded-b-md">
          { data.name }
        </BaseNodeFooter>
      </BaseNode>

      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className='flex flex-row items-center'>
              <FunctionLibraryIcon className="w-4 h-4 mr-2" />
              修改自定义函数库
            </DialogTitle>
          </DialogHeader>

          <FunctionLibraryForm {...node} setFormOpen={setShowForm}/>
        </DialogContent>
      </Dialog>
    </div>
  );
}
