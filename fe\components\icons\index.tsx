import {
  Camera,
  BookText,
  Monitor,
  History,
  User,
  Settings2,
  ChevronsLeftRightEllipsis,
  Thermometer,
  ArrowRight,
  ArrowLeft,
  Warehouse
} from 'lucide-react'
import { icons, type Icon as LucideIcon } from "lucide-react";

export const DeviceIcon = Camera
export const FormulaIcon = BookText
export const MonitorIcon = Monitor
export const HistoryIcon = History
export const UserIcon = User
export const SettingsIcon = Settings2
export const TargetIcon = Monitor


export const ConnectionChannelIcon = ChevronsLeftRightEllipsis
export const SensorIcon = Thermometer
export const InputVariableIcon = ArrowRight
export const OutputVariableIcon = ArrowLeft
export const FunctionLibraryIcon = Warehouse


interface IconProps {
  name: keyof typeof icons;
  size?: number;
  color?: string;
}

export const Icon = ({ name, size = 24, color = "currentColor" }: IconProps) => {
  // @ts-expect-error "omit"
  const LucideIcon = icons[name] as LucideIcon;

  if (!LucideIcon) {
    console.warn(`Icon "${name}" not found in Lucide`);
    return null;
  }

  return <LucideIcon size={size} color={color} />;
};

