export type ListInfoItem = {
  key: string;
  value: string | number
}

export function ListInfo({ items }: { items: ListInfoItem[] }) {
  return (
    <div className="flex flex-col gap-2 border-r border-l px-2 h-full">
      { items.map((item) => (
        <div key={item.key} className="flex items-center gap-2 justify-between">
          <span className="font-semibold inline-block">{item.key}:</span>
          <span className="text-gray-700 inline-block">{item.value}</span>
        </div>)) }
    </div>
  )
}
