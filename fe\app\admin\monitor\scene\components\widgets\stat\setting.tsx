import React, { useState } from 'react';

import { useAppStore, SceneStore, FormulaStore } from '@/app/admin/store'
import { StatRender } from './render';

import { Form, FormItem, FormControl,FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectItem, SelectContent } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {  ColorPicker } from '@/components/color-picker';

import { fontSizes } from '@/app/admin/monitor/scene/components/constants';

import { TStatWidgetProps, statWidgetPropsSchema } from '@/types/stat-widget-props';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { shallow } from 'zustand/shallow';

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { Button } from "@/components/ui/button";
import { SettingCard } from '@/app/admin/monitor/scene/components/setting-card';

export type StatSettingProps = {
  id: string;
}

const selectors = (id: string) => (state: SceneStore & FormulaStore ) => ({
  widget: state.widgets.find((w) => w.id === id),
  updateWidgetProps: state.updateWidgetProps,
  outputVarnames: state.nodes.filter((n) => n.type === 'output-variable').map((n) => n.data.varname),
})

export function StatSetting({ id }: StatSettingProps) {
  const { widget, updateWidgetProps, outputVarnames } = useAppStore(selectors(id), shallow);
  const props = (widget?.props || {}) as TStatWidgetProps;

  const form = useForm<Partial<TStatWidgetProps>>({
    resolver: zodResolver(statWidgetPropsSchema),
    defaultValues: props
  })

  const onSubmit = (data: Partial<TStatWidgetProps>) => {
    updateWidgetProps(id, data as TStatWidgetProps);
  }

  const [color, setColor] = useState(props && props.color || '#000000');

  return (<div className='w-full h-full flex-col items-center justify-between'>
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='flex items-center justify-between bg-gray-100 p-2 space-x-2'>
          <div className='flex flex-col items-center justify-center w-full h-full basis-3/4 '>
            <Card className='rounded-none shadow-sm'>
              <CardHeader>
                <CardTitle>{ props.widgetName || "发动机转速" }</CardTitle>
              </CardHeader>
              <CardContent className='flex flex-col items-center justify-center h-full'>
                <StatRender id={id} {...props} />
              </CardContent>
            </Card>
          </div>

          <div className="flex flex-col items-center justify-center space-y-2 basis-1/4">
            <SettingCard title="图表配置">
              <FormField
                control={form.control}
                name="title"
                render={({field}) => (
                  <FormItem className='flex flex-row'>
                    <FormLabel className='text-gray-400'>标题</FormLabel>
                    <FormControl>
                      <Input placeholder="图表标题" {...field}  />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fontSize"
                render={({field}) => (
                  <FormItem className='flex flex-row'>
                    <FormLabel>字号</FormLabel>
                    <FormControl>
                      <Select onValueChange={(v: string) => { field.onChange(Number(v)) }} defaultValue={field.value?.toString()}>
                        <SelectTrigger className='col-span-3 w-full'>
                          <SelectValue placeholder="字号" />
                        </SelectTrigger>

                        <SelectContent>
                          { fontSizes.map((size) => ( <SelectItem key={size} value={size.toString()}>{size}</SelectItem>)) }
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="color"
                render={({field}) => (
                  <FormItem className='flex flex-row'>
                    <FormLabel >颜色</FormLabel>
                    <FormControl>
                      <div className='flex flex-row'>
                        <Input placeholder="" {...field}  />
                        <ColorPicker
                          onChange={(v: string) => {
                            form.setValue('color', v);
                            setColor(v);
                          }}
                          color={color}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </SettingCard >

            <SettingCard title="数据配置">
              <FormField
                control={form.control}
                name="widgetName"
                render={({field}) => (
                  <FormItem className='flex flex-row'>
                    <FormLabel className='text-normal'>名称</FormLabel>
                    <FormControl>
                      <Input placeholder="" {...field}  />
                    </FormControl>
                    <FormMessage className='text-right'> { form.formState.errors.title ? form.formState.errors.title.message : '' } </FormMessage>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unit"
                render={({field}) => (
                  <FormItem className='flex flex-row'>
                    <FormLabel>单位</FormLabel>
                    <FormControl> 
                      <Input placeholder="" {...field}  /> 
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="outputVarname"
                render={({field}) => (
                  <FormItem className='flex flex-row'>
                    <FormLabel>输出变量</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger className='col-span-3 w-full'>
                          <SelectValue placeholder="选择输出变量" />
                        </SelectTrigger>

                        <SelectContent>
                          { outputVarnames.map((type, i) => (
                            <SelectItem key={i} value={type as string}>{type as string}</SelectItem>
                          )) }
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </FormItem>
                )}
              />
            </SettingCard>


            <SettingCard title="数据预览">
              <FormField
                control={form.control}
                name="sampleData"
                render={({field}) => (
                  <FormItem className='flex flex-row'>
                    <FormLabel>预览数据</FormLabel>
                    <FormControl>
                      <Textarea placeholder="" {...field}  />
                    </FormControl>
                  </FormItem>
                )}
              />
            </SettingCard>
          </div>
        </div>
        <div className='flex flex-row items-center justify-end w-full h-12 bg-white p-2 space-x-2'>
          <Button className='cursor-pointer rounded-none' variant="secondary">重置</Button>
          <Button className='cursor-pointer rounded-none'>确定</Button>
        </div>
      </form>
    </Form>
  </div>
  )
}

