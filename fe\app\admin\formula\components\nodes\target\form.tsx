import React from 'react';
import { NodeProps, useReactFlow } from '@xyflow/react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TTargetSchema, targetTypeValues, targetSchema } from '@/types/target';

import { Form, FormItem, FormControl, FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectItem, SelectContent } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { TargetNode } from './node'

export type TargetFormProps = {
  setFormOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const TargetForm: React.FC<NodeProps<TargetNode> & TargetFormProps> = ({
id, data, setFormOpen
}) => {
  const {setNodes} = useReactFlow();

  const form = useForm<Partial<TTargetSchema>>({
    resolver: zodResolver(targetSchema),
    defaultValues: {
      ...{
        name: '',
        measurePt: '',
      },
      ...data,
    }
  });

  const onSubmit = (data: Partial<TTargetSchema>) => {
    setNodes((nodes) => {
      return nodes.map((node) => {
        if (node.id === id) { return { ...node, data } }

        return node
      })
    })

    setFormOpen(false);
  }


  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-2 p-2"
      >
        <FormField
          control={form.control}
          name="name"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>名称</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>

              <FormMessage className='text-right'>
                {
                  form.formState.errors.name ? form.formState.errors.name.message : ''
                }
              </FormMessage>

            </FormItem>

          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>类型</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger className='col-span-3 w-full'>
                      <SelectValue placeholder="选择传感器类型" />
                    </SelectTrigger>

                    <SelectContent>
                      { targetTypeValues.map((type) => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      )) }
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.type ? form.formState.errors.type.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />


        <FormField
          control={form.control}
          name="measurePt"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>测点</FormLabel>
                <FormControl>
                  <Textarea placeholder="" {...field} className='col-span-3'/>
                </FormControl>
              </div>
              <FormMessage></FormMessage>
            </FormItem>
          )}
        />

        <Button type="submit" className='mt-4 cursor-pointer' disabled={form.formState.isSubmitting} >提交</Button>
      </form>
    </Form>
  );
};
