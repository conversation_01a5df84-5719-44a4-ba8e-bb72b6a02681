'use client'

import React, { useState, useEffect } from 'react';
import { TStatWidgetProps } from '@/types/stat-widget-props';


export type StatRenderProps = {
  id: string;
} & TStatWidgetProps;

export function StatRender( { unit, widgetName, color, fontSize, sampleData, outputVarname  }: StatRenderProps)  {
  const [ value, setValue  ] = useState<number>(0);

  useEffect(() => {
    try {
      const j = JSON.parse(sampleData);
      setValue(j[outputVarname]?.num || 0);
    }catch(e: unknown) {
      console.error("statWidgetSampleDataSchema error", e);
      setValue(0);
    }
  }, [sampleData, setValue, outputVarname]);


  return (
    <div className="flex flex-col items-center justify-center h-full">
      { unit || "RPM"}
      <div className="text-2xl font-bold" style={{ color: color, fontSize: `${fontSize}px` }}> { value || "10000"} </div>
      { widgetName && ( <div className="text-sm text-muted-foreground"> { widgetName || "发动机转速"} </div> )}
    </div>
  );
}
