import React, { useState } from 'react';
import { BarRender } from './render';

import { Form, FormItem, FormControl, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {  ColorPicker } from '@/components/color-picker';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { Button } from "@/components/ui/button";
import { SettingCard } from '@/app/admin/monitor/scene/components/setting-card';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { shallow } from 'zustand/shallow';
import { TBarWidgetProps, barWidgetPropsSchema } from '@/types/bar-widget-props';

import { useAppStore, FormulaStore, SceneStore } from '@/app/admin/store'

export type BarSettingProps = {
  id: string;
}

const selector = (id: string) => (state: FormulaStore & SceneStore) => ({
  widget: state.widgets.find((w) => w.id === id),
  updateWidgetProps: state.updateWidgetProps,
  outputVarnames: state.nodes.filter((n) => n.type === 'output-variable').map((n) => n.data.varname),
})

export function BarSetting({ id } :BarSettingProps) {
  const { widget, updateWidgetProps } = useAppStore(selector(id), shallow);
  const props = (widget?.props || {}) as TBarWidgetProps;

  const form = useForm<Partial<TBarWidgetProps>>({
    resolver: zodResolver(barWidgetPropsSchema),
    defaultValues: {
      ...{
        title:  "发动机转速",
        barColor: '#000000',
        minYValue: 0,
        maxYValue: 100,
        outputVarnames: 'output1',
        sampleData: '[]',
      },
      ...props,
    }
  })

  const onSubmit = (data: Partial<TBarWidgetProps>) => {
    updateWidgetProps(id, data as TBarWidgetProps);
  }

  const [color, setColor] = useState(props && props.barColor || '#000000');
  return (<div className='w-full h-full flex-col items-center justify-between'>
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className='flex items-center justify-between bg-gray-100 p-2 space-x-2'>
          <div className='flex flex-col items-center justify-center w-full h-full basis-3/4 '>
            <Card className='rounded-none shadow-sm'>
              <CardHeader>
                <CardTitle>{ props.title || "发动机转速" }</CardTitle>
              </CardHeader>
              <CardContent className='flex flex-col items-center justify-center h-full'>
                <BarRender id={id} {...props} />
              </CardContent>
            </Card>
          </div>

          <div className="flex flex-col items-center justify-center space-y-2 basis-1/4">
            <SettingCard title="图表配置">
              <FormField
                control={form.control}
                name="title"
                render={({field}) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel className='text-gray-400'>标题</FormLabel>
                    <FormControl>
                      <Input placeholder="图表标题" {...field}  />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="barColor"
                render={({field}) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel className='text-gray-400'>颜色</FormLabel>
                    <FormControl>
                      <div className='flex flex-row'>
                        <Input placeholder="" {...field}  />
                        <ColorPicker
                          onChange={(v: string) => {
                            form.setValue('barColor', v);
                            setColor(v);
                          }}
                          color={color}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </SettingCard >

            <SettingCard title="数据配置">
              <FormField
                control={form.control}
                name="maxYValue"
                render={({field}) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel className='text-gray-400'>Y轴最大值</FormLabel>
                    <FormControl>
                      <Input placeholder="" {...field}  /> 
                    </FormControl>
                  </FormItem>
                )}
              />
            </SettingCard>


            <SettingCard title="数据预览">
              <FormField
                control={form.control}
                name="sampleData"
                render={({field}) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel className='text-gray-400'>预览数据</FormLabel>
                    <FormControl>
                      <Textarea placeholder="" {...field}  />
                    </FormControl>
                  </FormItem>
                )}
              />
            </SettingCard>
          </div>
        </div>
        <div className='flex flex-row items-center justify-end w-full h-12 bg-white p-2 space-x-2'>
          <Button className='cursor-pointer rounded-none' variant="secondary">重置</Button>
          <Button className='cursor-pointer rounded-none'>确定</Button>
        </div>
      </form>
    </Form>
  </div>
  )
}
