import React from 'react';

import { Form, FormItem, FormControl, FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Select, SelectTrigger, SelectValue, SelectItem, SelectContent } from '@/components/ui/select';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Button } from '@/components/ui/button';

import { widgetTypeNames } from './constants';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import type { infer as zodInfer } from 'zod';


export type AddWidgetFormProps = {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onAddWidgetWithType: (type: string) => void;
}

export function AddWidgetForm({
  isOpen,
  setIsOpen,
  onAddWidgetWithType
}:AddWidgetFormProps) {

  const schema = z.object({ type: z.enum(
    Object.keys(widgetTypeNames) as [string, ...string[]],
  ) })
  type TSchema = zodInfer<typeof schema>;

  const form = useForm<TSchema>({
    resolver: zodResolver(schema),
    defaultValues: {
      type: 'stat' // 默认值为 'stat'
    }
  });

  const onSubmit = (data: TSchema) => {
    console.log('Form submitted with data:', data);
    onAddWidgetWithType(data.type);
    setIsOpen(false);
  }
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen} >
      <DialogContent className='max-w-2xl p-4'>
        <DialogHeader >
          <DialogTitle className='flex flex-row items-center'>
            创建图表
          </DialogTitle>
        </DialogHeader>


        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="type"
              render={({field}) => (
                <FormItem>
                  <div className="grid grid-cols-4 items-center gap-2">
                    <FormLabel className='col-span-1'>图表类型</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger className='col-span-3 w-full'>
                          <SelectValue placeholder="选择图表类型" />
                        </SelectTrigger>

                        <SelectContent>
                          { Object.entries(widgetTypeNames).map(([type, name]) => (
                            <SelectItem key={type} value={type}>{name}</SelectItem>
                          )) }
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </div>
                  <FormMessage className='text-right'>
                    {
                      form.formState.errors.type ? form.formState.errors.type.message : ''
                    }
                  </FormMessage>
                </FormItem>
              )}
            />
            <Button type="submit" className='mt-4 cursor-pointer' disabled={form.formState.isSubmitting} >新建</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
