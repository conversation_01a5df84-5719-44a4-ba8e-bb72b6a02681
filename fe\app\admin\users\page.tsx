'use client';

import { Button } from "@/components/ui/button";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const users = [
  {
    id: 1,
    account: "andy",
    dept: "部门 1",
    permission: "admin",
    description: "张三李四王五"
  },

  {
    id: 2,
    account: "belic",
    dept: "部门 1",
    permission: "admin",
    description: "张三李四王五"
  },

  {
    id: 3,
    account: "cendy",
    dept: "部门 1",
    permission: "admin",
    description: "张三李四王五"
  },

  {
    id: 4,
    account: "david",
    dept: "部门 1",
    permission: "admin",
    description: "张三李四王五"
  },
]

export default function Page() {
  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex flex-row gap-4 justify-between items-center h-full">
        <div>场景 - 场景列表</div>
      </div>

      <Card className="shadow-lg rounded-none">
        <CardHeader className=''>
          <CardTitle>观测员列表</CardTitle>
          <CardDescription></CardDescription>

          <CardAction>
            <Button className='cursor-pointer rounded-none'>新增</Button>
          </CardAction>
        </CardHeader>
        <CardContent>

          <Table>
            <TableCaption></TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">ID</TableHead>
                <TableHead>账号</TableHead>
                <TableHead>部门</TableHead>
                <TableHead>权限</TableHead>
                <TableHead>简介</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">{invoice.id}</TableCell>
                  <TableCell className="font-medium">{invoice.account}</TableCell>
                  <TableCell>{invoice.dept}</TableCell>
                  <TableCell>{invoice.permission}</TableCell>
                  <TableCell>{invoice.description}</TableCell>
                </TableRow>
              ))}
            </TableBody>
            <TableFooter>
              <TableRow>
              </TableRow>
            </TableFooter>
          </Table>
        </CardContent>
        <CardFooter className="flex flex-row items-center justifiy-between space-x-2">
        </CardFooter>
      </Card>
    </div>
  )
}
