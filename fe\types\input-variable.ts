import { z } from 'zod';

export const inputVariableTypeValues = ['整形', '浮点型', '字符串', '布尔型'] as const;
export type InputVariableType = typeof inputVariableTypeValues[number];

export const inputVariableSchema =  z.object({
  name: z.string().min(1, '变量名称不能为空').max(100, '变量名称不能超过100个字符').default('输入变量1'), // 变量名称
  type: z.enum(inputVariableTypeValues).default('整形'), // 变量类型
  varname: z.string().regex(/^[a-zA-Z_$][a-zA-Z0-9_$]*$/, '变量名必须以字母、下划线或美元符号开头，后续可以包含字母、数字、下划线或美元符号').max(100, '变量名不能超过100个字符').default(''), // 变量名
  value: z.string().max(500, '变量值不能超过500个字符').default(''), // 变量值
  description: z.string().max(500, '变量描述不能超过500个字符').optional(), // 变量描述
})

export type TInputVariableSchema = z.infer<typeof inputVariableSchema>;
