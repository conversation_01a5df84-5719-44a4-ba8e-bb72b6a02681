import React, { useState } from 'react';
import { type Node, type NodeProps, Position } from '@xyflow/react';

import {
  BaseNode,
  BaseNodeContent,
  BaseNodeFooter,
  BaseNodeHeader,
  BaseNodeHeaderTitle,
} from '@/components/base-node';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LabeledHandle } from '@/components/labeled-handle';
import Editor from '@monaco-editor/react';

import { ConfirmDialog } from '@/components/confirm-dialog';
import { Trash2, Pencil } from 'lucide-react';
import { FormulaIcon } from '@/components/icons';
import { FormulaForm } from './form';
import { TFormulaSchema } from '@/types/formula';
import { gradientStyle, cn, textColorStyle, defaultHandleStyle } from '@/lib/utils';
import { nodeColors } from '../../constants';
import { useAppStore } from '@/app/admin/store';

export type FormulaNode = Node<TFormulaSchema>;

export function FormulaNode(node: NodeProps<FormulaNode>) {
  const { id, data } = node;
  const [onDeleteConfirmOpen, setOnDeleteConfirmOpen] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const { deleteNode } = useAppStore((state) => ({
    deleteNode: state.deleteNode,
  }))
  const [handleStyle, setHandleStyle] = useState<React.CSSProperties>({})

  return (
    <div className="group">
      <div className="flex p-3 space-x-2 justify-center items-center bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Trash2 onClick={() => { setOnDeleteConfirmOpen(true) }} className='w-4 h-4'/>
        <ConfirmDialog
          isOpen={onDeleteConfirmOpen}
          onClose={() => setOnDeleteConfirmOpen(false)}
          title="删除公式"
          description="确定要删除此公式？"
          onConfirm={() => {deleteNode(id) }}
        />

        <Pencil className='w-4 h-4'/>
      </div>

      <BaseNode onClick={() => setShowForm(true)}
        onMouseEnter={() => {
          setHandleStyle(defaultHandleStyle())
        }}
        onMouseLeave={() => {
          setHandleStyle({})
        }}>
        <BaseNodeHeader className={cn("min-w-[240px] items-center justify-between py-2 rounded-md")}
          style={ gradientStyle(nodeColors['formula'])  }
        >
          <LabeledHandle title="" type="target" position={Position.Left} style={handleStyle}/>
          <FormulaIcon className={cn("w-4 h-4") } 
           style={textColorStyle(nodeColors['formula'])} />
          <BaseNodeHeaderTitle>{ data.name }</BaseNodeHeaderTitle>
        </BaseNodeHeader>

        <BaseNodeContent className="p-2 relative">
          <Editor
            height="300px"
            defaultLanguage="python"
            value={data.code}
            options={{
              minimap: { enabled: false },
              fontSize: 14,
              wordWrap: "on",
              lineNumbers: "off",
              readOnly: true,
            }}
          />
        </BaseNodeContent>

        <BaseNodeFooter className="bg-gray-100 px-0 py-1 w-full rounded-b-md">
          { data.name }
        </BaseNodeFooter>
      </BaseNode>

      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className='flex flex-row items-center'>
              <FormulaIcon className="w-4 h-4 mr-2" />
              修改公式
            </DialogTitle>
          </DialogHeader>

          <FormulaForm {...node} setFormOpen={setShowForm}/>
        </DialogContent>
      </Dialog>
    </div>
  );
}
