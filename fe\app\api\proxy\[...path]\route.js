// app/api/proxy/[...path]/route.js
import { NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'https://your-backend-api.com';

async function handler(request, { params }) {

  const { path } = await params;
  const searchParams = request.nextUrl.searchParams;
  const method = request.method;

  try {
    const url = new URL(path.join('/'), BACKEND_URL);
    searchParams.forEach((value, key) => url.searchParams.append(key, value));

    const forwardedRequest = {
      method,
      headers: {
        ...Object.fromEntries(request.headers.entries()),
        host: new URL(BACKEND_URL).host, // Fix host header
      },
    };

    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      forwardedRequest.body = await request.text();
    }

    const response = await fetch(url, forwardedRequest);

    if (!response.ok) {
      throw new Error(`Backend returned ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    return NextResponse.json(
      { error: error.message },
      { status: error.response?.status || 500 }
    );
  }
}

export const GET = handler;
export const POST = handler;
export const PUT = handler;
export const PATCH = handler;
export const DELETE = handler;

