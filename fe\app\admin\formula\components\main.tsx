'use client';

import React, { useCallback, useEffect } from 'react';
import {
  ReactFlow,
  Panel,
  Background,
  Controls,
  MiniMap,
  useReactFlow,
} from '@xyflow/react';
import { nodeColors } from '@/app/admin/formula/components/constants';
import '@xyflow/react/dist/style.css';
import { RefreshCw, CircleQuestionMark } from 'lucide-react';
import { Button } from '@/components/ui/button';

import {
  nodeTypes,
  edgeTypes,
} from './constants';

import { shallow } from 'zustand/shallow';
import { useAppStore, FormulaStore} from '@/app/admin/store';

import { devicesData } from '@/lib/mock';

const selector = (state: FormulaStore) => ({
  nodes: state.nodes,
  edges: state.edges,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
  addDeviceNodeIfNotExist: state.addDeviceNodeIfNotExist,
  onConnect: state.onConnect,
  clearNodesAndEdges: state.clearNodesAndEdges,
  addNewNode: state.addNewNode,
  isValidConnection: state.isValidConnection,
})

export function Main() {
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    addDeviceNodeIfNotExist,
    onConnect,
    clearNodesAndEdges,
    isValidConnection,
    addNewNode,
  } = useAppStore(selector, shallow);

  const { fitView } = useReactFlow();
  const addNewNodeAndFitView = useCallback((type: string) => {
    const node = addNewNode(type);
    fitView({ nodes: [node], duration: 500, interpolate: 'smooth' });
  }, [fitView, addNewNode]);

  useEffect(() => {
    devicesData.map((device) => {
      addDeviceNodeIfNotExist(device);
    })
  }, [addDeviceNodeIfNotExist]);

  return (
    <div className="w-full h-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        isValidConnection={isValidConnection}
        onConnectStart={(event, params) => {
          console.log('onConnectStart', event, params);
        }}
        onConnectEnd={(event, params) => {
          console.log('onConnectEnd', event, params);
        }}
        fitView
      >
        <Panel position="top-right" className="shadow-none space-x-1">
          <Button
            style={{ backgroundColor: nodeColors['sensor']}}
            onClick={() => addNewNodeAndFitView('sensor')}
          >传感器</Button>

          <Button
            style={{ backgroundColor: nodeColors['connection-channel']}}
            onClick={() => addNewNodeAndFitView('connection-channel')}
          >连接通道</Button>

          <Button
            style={{ backgroundColor: nodeColors['target']}}
            onClick={() => addNewNodeAndFitView('target')}
          >测量模型</Button>

          <Button
            style={{ backgroundColor: nodeColors['input-variable']}}
            onClick={() => addNewNodeAndFitView('input-variable')}
          >输入变量</Button>

          <Button
            style={{ backgroundColor: nodeColors['formula']}}
            onClick={() => addNewNodeAndFitView('formula')}
          >公式</Button>

          <Button
            style={{ backgroundColor: nodeColors['output-variable']}}
            onClick={() => addNewNodeAndFitView('output-variable')}
          >输出变量</Button>

          <Button
            style={{ backgroundColor: nodeColors['function-library']}}
            onClick={() => addNewNodeAndFitView('function-library')}
          >自定义函数库</Button>
        </Panel>

        <Panel position="top-left" className="shadow-none">
          <div className="flex items-center space-x-2">
            <RefreshCw className='h-4 w-4 ' onClick={clearNodesAndEdges}/>
            <CircleQuestionMark className='h-4 w-4' />
          </div>
        </Panel>
        <Background />
        <Controls />
        <MiniMap zoomable pannable/>
      </ReactFlow>
    </div>
  );
}
