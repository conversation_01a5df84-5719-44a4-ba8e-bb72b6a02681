import axios, { AxiosResponse, AxiosRequestConfig, RawAxiosRequestHeaders } from 'axios';


const config: AxiosRequestConfig = {
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  } as RawAxiosRequestHeaders,
};

export const api = axios.create(config);

export async function healthCheck(): Promise<boolean> {
  const resp: AxiosResponse = await api.get('/api/proxy/health');
  console.log('Get Health check response:', resp.status, resp.data);

  const postResp: AxiosResponse = await api.post('/api/proxy/health', { test: 'test' });
  console.log('Post Health check response:', postResp.status, postResp.data);

  const getResp: AxiosResponse = await api.get('/api/proxy/health');
  console.log('Get Health check response:', getResp.status, getResp.data);

  const deleteResp: AxiosResponse = await api.delete('/api/proxy/health');
  console.log('Delete Health check response:', deleteResp.status, deleteResp.data);

  const putResp: AxiosResponse = await api.put('/api/proxy/health', { test: 'test' });
  console.log('Put Health check response:', putResp.status, putResp.data);

  return resp.status === 200
}

