import React, { useState } from 'react';
import { type Node, type NodeProps, Position } from '@xyflow/react';

import {
  BaseNode,
  BaseNodeContent,
  BaseNodeFooter,
  BaseNodeHeader,
  BaseNodeHeaderTitle,
} from '@/components/base-node';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LabeledHandle } from '@/components/labeled-handle';

import { ListInfo } from '@/components/list-info';
import { ConfirmDialog } from '@/components/confirm-dialog';
import { Trash2, Pencil } from 'lucide-react';
import { TargetIcon } from '@/components/icons';
import { TargetForm } from './form';

import { TTargetSchema } from '@/types/target';
import { shortStringInEnd, gradientStyle, cn, textColorStyle, defaultHandleStyle } from '@/lib/utils';

import { nodeColors } from '../../constants';
import { useAppStore } from '@/app/admin/store';

export type TargetNode = Node<TTargetSchema>;

export function TargetNode(node: NodeProps<TargetNode>) {
  const { id, data } = node;
  const [onDeleteConfirmOpen, setOnDeleteConfirmOpen] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const { deleteNode } = useAppStore((state) => ({
    deleteNode: state.deleteNode,
  }))

  const rendAttributes = (data: TTargetSchema) => {
    const attributes = [
      {key: 'ID', value: shortStringInEnd(data.id) || '未知id'},
      { key: '名称', value: data.name || '未知名称' },
      { key: '属性-甲', value: data.name || '未知名称' },
      { key: '属性-乙', value: data.name || '未知名称' },
      { key: '属性-丙', value: data.name || '未知名称' },
      { key: '属性-丁', value: data.name || '未知名称' },
    ]

    return <ListInfo items={attributes} />
  }

  const [handleStyle, setHandleStyle] = useState<React.CSSProperties>({})

  return (
    <div className="group">
      <div className="flex p-3 space-x-2 justify-center items-center bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Trash2 onClick={() => { setOnDeleteConfirmOpen(true) }} className='w-4 h-4'/>
        <ConfirmDialog
          isOpen={onDeleteConfirmOpen}
          onClose={() => setOnDeleteConfirmOpen(false)}
          title="删除测量模型"
          description="确定要删除此测量模型？"
          onConfirm={() => {deleteNode(id) } }
        />

        <Pencil className='w-4 h-4'/>
      </div>

      <BaseNode onClick={() => setShowForm(true)}
        onMouseEnter={() => {
          setHandleStyle(defaultHandleStyle())
        }}
        onMouseLeave={() => {
          setHandleStyle({})
        } }
      >
        <BaseNodeHeader className={cn("min-w-[240px] items-center justify-between py-2 rounded-md")}
          style={ gradientStyle(nodeColors['target'])  }
        >
          <TargetIcon className={cn("w-4 h-4")}
            style={textColorStyle(nodeColors['target'])} 
          />
          <BaseNodeHeaderTitle>{ data.name }</BaseNodeHeaderTitle>
          <LabeledHandle title="" type="source" position={Position.Right} style={handleStyle} />
        </BaseNodeHeader>

        <BaseNodeContent>
          { rendAttributes(data) }
        </BaseNodeContent>
        <BaseNodeFooter className="bg-gray-100 px-0 py-1 w-full">
          { data.name }
        </BaseNodeFooter>
      </BaseNode>

      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className='flex flex-row items-center'>
              <TargetIcon className="inline-block mr-2 w-4 h-4 text-primary" />
              修改测量模型
            </DialogTitle>
          </DialogHeader>

          <TargetForm {...node} setFormOpen={setShowForm}/>
        </DialogContent>
      </Dialog>
    </div>
  );
}
