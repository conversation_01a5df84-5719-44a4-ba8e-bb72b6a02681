
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"


export default function Page() {
  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex flex-row gap-4 justify-between items-center h-full">
        <div>工具 - 常用工具</div>
      </div>

      <div className="grid grid-cols-5 md:grid-cols-5 lg:grid-cols-5 sm:grid-cols-2 gap-8">
        <Card className="shadow-lg rounded-none cursor-pointer hover:transform hover:scale-105 transition-transform duration-300 border-t-4  border-primary border-b-0 border-x-0">
          <CardHeader className=''>
            <CardTitle >项目配置导出</CardTitle>
            <CardDescription></CardDescription>
          </CardHeader>
          <CardContent>
            快速导出当前项目的配置文件，包含所有传感器、公式、场景等信息。
          </CardContent>
          <CardFooter className="justify-end flex flex-row items-center space-x-2">
            <Button className='cursor-pointer rounded-none'>立即使用</Button>
          </CardFooter>
        </Card>

        <Card className="shadow-lg rounded-none cursor-pointer hover:transform hover:scale-105 transition-transform duration-300  border-t-4  border-primary border-b-0 border-x-0">
          <CardHeader>
            <CardTitle>数据导出</CardTitle>
            <CardDescription></CardDescription>
          </CardHeader>
          <CardContent>
            快速导出当前项目的配置文件，包含所有传感器、公式、场景等信息。
          </CardContent>
          <CardFooter className="justify-end flex flex-row items-center space-x-2">
            <Button className='cursor-pointer rounded-none'>立即使用</Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
