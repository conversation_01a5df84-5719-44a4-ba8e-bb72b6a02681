import { z } from 'zod';

export const sensorTypeValues = ['位移', '速度', '加速度', '应变片', 'kulite', '热电偶', 'BTT光纤'] as const
export type SensorType = typeof sensorTypeValues[number];

export const formulaTypeValues = ['无', '线性', '差值', '公式']
export type SensorFormulaType = typeof formulaTypeValues[number];

export const sensorSchema = z.object({
  id: z.string().uuid().default(() => crypto.randomUUID()), // 传感器ID
  name: z.string().min(1, '传感器名称不能为空').max(100, '传感器名称不能超过100个字符').default('新传感器'), // 传感器名称
  type: z.enum(sensorTypeValues).default("位移"), // 传感器类型
  model: z.string().max(100, '传感器型号不能超过100个字符').optional(),
  description: z.string().max(500, '传感器描述不能超过500个字符').optional(),
  formulaType: z.enum(formulaTypeValues).default('无'), // 传感器公式类型
  formula: z.string().max(500, '传感器公式不能超过500个字符').optional(),
  formulaUnit: z.string().max(50, '传感器公式单位不能超过50个字符').optional(),
})
export type TSensorSchema = z.infer<typeof sensorSchema>;
