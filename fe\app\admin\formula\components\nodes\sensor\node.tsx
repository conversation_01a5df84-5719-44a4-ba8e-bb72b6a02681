import React, { useState } from 'react';
import { type Node, type NodeProps, Position } from '@xyflow/react';

import {
  BaseNode,
  BaseNodeHeader,
  BaseNodeHeaderTitle,
} from '@/components/base-node';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LabeledHandle } from '@/components/labeled-handle';

import { ConfirmDialog } from '@/components/confirm-dialog';
import { Trash2, Pencil } from 'lucide-react';
import { SensorIcon } from '@/components/icons';
import { SensorForm } from './form';

import { TSensorSchema } from '@/types/sensor';
import { nodeColors } from '../../constants';
import { cn, gradientStyle, defaultHandleStyle } from '@/lib/utils';

import { useAppStore } from '@/app/admin/store';

export type SensorNode = Node<TSensorSchema>;

export function SensorNode(node: NodeProps<SensorNode>) {
  const { id, data } = node;
  const { deleteNode } = useAppStore((state) => ({
    deleteNode: state.deleteNode,
  }))
  const [onDeleteConfirmOpen, setOnDeleteConfirmOpen] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const [handleStyle, setHandleStyle] = useState<React.CSSProperties>({})

  return (
    <div className="group">
      <div className="flex p-3 space-x-2 justify-center items-center bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Trash2 onClick={() => { setOnDeleteConfirmOpen(true) }} className='w-4 h-4'/>
        <ConfirmDialog
          isOpen={onDeleteConfirmOpen}
          onClose={() => setOnDeleteConfirmOpen(false)}
          title="删除传感器"
          description="确定要删除此传感器？"
          onConfirm={() => { deleteNode(id) }}
        />

        <Pencil className='w-4 h-4'/>
      </div>

      <BaseNode 
        onClick={() => setShowForm(true)}
        onMouseEnter={() => {
          setHandleStyle(defaultHandleStyle())
        }}
        onMouseLeave={() => {
          setHandleStyle({})
        }}>

        <BaseNodeHeader className={cn("min-w-[240px] items-center justify-between py-2 rounded-md")}
          style={ gradientStyle(nodeColors['sensor'])  }
        >
          <SensorIcon className={cn("w-4 h-4") } />
          <BaseNodeHeaderTitle>{ data.name }</BaseNodeHeaderTitle>
          <LabeledHandle title="" type="source" position={Position.Right} style={handleStyle}/>
        </BaseNodeHeader>
      </BaseNode>

      <Dialog open={showForm} onOpenChange={setShowForm} >
        <DialogContent>
          <DialogHeader >
            <DialogTitle className='flex flex-row items-center'>
              <SensorIcon className={cn("w-4 h-4") } />
              修改传感器
            </DialogTitle>
          </DialogHeader>

          <SensorForm {...node} setFormOpen={setShowForm}/>
        </DialogContent>
      </Dialog>
    </div>
  );
}
