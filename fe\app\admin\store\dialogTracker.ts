export interface DialogTrackerStore {
  openDialogs: string[];
  openDialog: (id: string) => void;
  closeDialog: (id: string) => void;
}


// @ts-expect-error "omit"
export function dialogTracker(set) {
  return {
    openDialogs: [],
    // @ts-expect-error "omit"
    openDialog: (id) => set((state) => ({
      openDialogs: [...state.openDialogs, id]
    })),

    // @ts-expect-error "omit"
    closeDialog: (id) => set((state) => ({

      // @ts-expect-error "omit"
      openDialogs: state.openDialogs.filter(dialogId => dialogId !== id)
    })),
  }
}

