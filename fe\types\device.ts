import { z } from 'zod';

export const deviceSchema =  z.object({
  sn: z.string().min(1, '设备SN不能为空').max(100, '设备SN不能超过100个字符').default('新设备'), // 设备SN
  name: z.string().min(1, '设备名称不能为空').max(100, '设备名称不能超过100个字符').default('新设备'), // 设备名称
  model: z.string().max(100, '设备型号不能超过100个字符').optional(), // 设备型号
  sampleRate: z.number().min(1, '采样率必须大于0').default(1000), // 采样率
  chNum: z.number().min(1, '通道数必须大于0').max(256, '通道数不能超过64').default(16), // 通道数
})

export type TDeviceSchema = z.infer<typeof deviceSchema>;
