import React from 'react';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { cn } from "@/lib/utils";

export function SettingCard({ className, title, children }: { title?: string, className?: string, children?: React.ReactNode }) {
  return (
    <Card className={cn('rounded-none shadow-sm py-0 gap-2', className)}>
      <CardHeader className='flex items-center justify-between p-2 min-w-[200px]'>
        <CardTitle>{ title }</CardTitle>
      </CardHeader>
      <CardContent className='flex flex-col p-2 border-none space-y-2'>
        { children}
      </CardContent>
    </Card>
  )
}
