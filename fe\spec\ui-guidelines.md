## UI 规范（Tailwind + shadcn 基准）

本文档规定 UI 设计与实现规范，默认遵循 Tailwind CSS 与 shadcn/ui 的约定，并与项目现有主题 Token（app/globals.css）对齐。所有新 UI 需尽量复用 components/ui 下的封装组件。

---

### 1. 颜色与主题

- 主题 Token（与 app/globals.css 保持一致）：
  - 语义色：--background、--foreground、--primary、--secondary、--muted、--accent、--destructive、--border、--input、--ring
  - 容器色：--card、--popover 及其 *-foreground
  - 其它：--sidebar*、--chart-1~5 等图表与侧栏色
- 使用准则（Tailwind 对应类）：
  - 页面背景/文本：bg-background、text-foreground
  - 容器：bg-card、text-card-foreground 或 bg-popover、text-popover-foreground
  - 主按钮/强调：bg-primary、text-primary-foreground；悬浮/按压使用 hover:bg-primary/90
  - 次级按钮：bg-secondary、text-secondary-foreground
  - 辅助/中性背景：bg-muted、text-muted-foreground
  - 破坏性操作/错误：bg-destructive、text-white 或 text-destructive（文本）
  - 边框与输入：border-border、bg-input
  - 焦点环：focus-visible:ring-ring/50（搭配 focus-visible:outline-none、focus-visible:border-ring）
- 暗色模式：
  - 通过 .dark 类切换（已在 globals.css 中定义 dark 变量）
  - 组件应使用语义类名（如 text-foreground、bg-card），避免硬编码具体颜色
- 图表与可视化：
  - 优先使用 --chart-1 ~ --chart-5，保证主题一致性
  - 同一图表内颜色对比需满足可读性，对暗色主题应逐一核对

---

### 2. 间距与布局

- Tailwind 间距刻度（rem）：0, 0.5(2px), 1(4px), 1.5(6px), 2(8px), 2.5(10px), 3(12px), 3.5(14px), 4(16px), 5(20px), 6(24px), 8(32px), 10(40px), 12(48px), 16(64px) …
- 页面与区块：
  - 容器内边距：px-6 md:px-8，纵向 py-6~10 视模块复杂度而定
  - 区块间距：gap-6 为常规；信息密集区可用 gap-4；空白区或页眉/页脚可用 gap-8~10
- 表单：
  - 控件分组间距：FormItem 默认 grid gap-2（8px），组与组之间建议 gap-4（16px）或 mb-4
  - 行内布局：使用 grid 或 flex，gap-4 作为行内控件间距基线
- 触达区域与尺寸：
  - 可点击元素高度不小于 32px（h-8）；按钮默认 h-9（36px），大号 h-10（40px）
  - 图标按钮使用 size-9（36px）保证触达

---

### 3. 排版与字体

- 字体：
  - Sans：Geist（--font-geist-sans）作为默认字体（font-sans）
  - Mono：Geist Mono（--font-geist-mono）用于代码/等宽内容（font-mono）
- 文本层级与字号（建议）：
  - 页面标题：text-2xl~3xl font-semibold/-bold，leading-tight
  - 模块标题：text-xl font-semibold，leading-snug
  - 正文：text-sm 或 text-base，leading-relaxed；辅助说明 text-sm text-muted-foreground
  - 代码/数值对齐：font-mono，text-sm~base
- 文本颜色：默认 text-foreground；弱化信息使用 text-muted-foreground；链接采用 text-primary hover:underline

---

### 4. 表单规范（RHF + zod + shadcn）

- 基础约定：
  - 表单统一使用 react-hook-form 与 zod（@hookform/resolvers）
  - 结构：Form（Provider）> FormField > FormItem（包含 FormLabel、FormControl、FormDescription、FormMessage）
  - 可访问性：
    - FormControl 自动注入 aria-invalid、aria-describedby
    - 错误信息使用 FormMessage（文本色 text-destructive）
- 校验与触发：
  - 模式：优先 onSubmit + onBlur 提示（表单提交失败时集中提示，失焦时提示单项）
  - 原则：
    - 规则由 zod 定义；默认值使用 utils.zodSchemaDefaultValues(schema)
    - 必填项：label 后加“*”（或必填说明），使用 required 规则
    - 格式错误/范围错误：即时在 FormMessage 展示；对复杂规则给出可操作建议
- 文案与占位：
  - Label 简洁明确；Description 提供上下文提示（text-sm text-muted-foreground）
  - Placeholder 用于示例格式，不代替 Label
- 交互细节：
  - 焦点：focus-visible:ring-[3px] ring-ring/50；错误态 aria-invalid:ring-destructive/20
  - 禁用：disabled:opacity-50 disabled:pointer-events-none，并在提交时禁用提交按钮

---

### 5. 错误、反馈与状态

- 行内错误（Inline）：
  - 使用 FormMessage；错误图标可选，保持简洁
- 全局/异步反馈：
  - 成功：toast.success（sonner），简短描述用户收益
  - 失败：toast.error，包含失败原因/下一步引导；避免仅显示“失败”
- 加载与占位：
  - 局部加载：按钮显示 loading 样式或禁用；字段级可显示 Skeleton/Spinner
  - 页面级加载：Skeleton 或空状态提示
- 确认与风险操作：
  - 破坏性操作使用 ConfirmDialog；按钮使用 variant="destructive"
  - 默认行为设置为“取消/关闭”，明确不可逆操作说明

---

### 6. 组件用法约定（shadcn 封装）

- Button（components/ui/button.tsx）：
  - 变体：
    - default 主操作；destructive 危险操作；secondary 次操作
    - outline 次级强调（边框）；ghost 文本/轻量操作；link 文字链接样式
  - 尺寸：default（h-9）、sm（h-8）、lg（h-10）、icon（size-9）
  - 焦点与无障碍：已内置 focus-visible 与 aria-invalid 样式
- Input/Select/Textarea：
  - 必须包裹在 FormItem 中，使用 FormLabel + FormControl + FormMessage
  - 占位符用于示例；错误信息统一在 FormMessage 渲染
- Dialog/Sheet/Popover/Tooltip：
  - 弹层优先使用 Dialog/Sheet，避免自定义裸 div；开启可关闭区域点击（如非关键表单）
  - Tooltip 用于微交互提示，不承载关键信息
- Sidebar/Tabs/Table/Card：
  - 保持与项目 style: "new-york" 一致的密度与留白；复杂表格使用 sticky 表头与 skeleton

---

### 7. 圆角、阴影与边框

- 圆角：项目变量 --radius: 0rem（整体方角风格）；若需局部圆角，使用 rounded-sm/md/lg 按设计需求
- 阴影：
  - 交互元素：shadow-xs/hover:shadow-sm
  - 卡片/弹层：shadow-sm~md，注意暗色模式下阴影层次
- 边框：
  - 默认使用 border-border；分隔线使用 border 或 separator 组件

---

### 8. 命名与可复用

- className 合并：统一使用 utils.cn()
- 颜色与尺寸尽量通过语义类和 Token，不直接写 hex/rgb
- 可复用模式抽象为组件：在 components/ui 或领域组件目录中沉淀

---

### 9. 示例片段

- 表单结构示例：

```tsx
<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
    <FormField
      control={form.control}
      name="name"
      render={({ field }) => (
        <FormItem>
          <FormLabel>名称 *</FormLabel>
          <FormControl>
            <Input placeholder="例如：设备 A" {...field} />
          </FormControl>
          <FormDescription>用于在列表中识别该对象。</FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
    <Button type="submit" disabled={form.formState.isSubmitting}>提交</Button>
  </form>
</Form>
```

- 按钮用法：

```tsx
<Button>主操作</Button>
<Button variant="secondary">次操作</Button>
<Button variant="outline">次级强调</Button>
<Button variant="ghost">轻量操作</Button>
<Button variant="link">链接样式</Button>
<Button variant="destructive">删除</Button>
```

---

### 10. 执行与评审

- 新增/修改 UI 需自查：
  - 是否使用语义 Token 与 Tailwind 类
  - 是否遵循表单结构、错误与焦点样式
  - 是否满足暗色模式与无障碍（键盘导航、焦点可见）
- 代码评审时按本规范进行校验，规范更新请在本文件维护。

