import React, { useState } from 'react';
import { type Node, type NodeProps, Position } from '@xyflow/react';

import {
  BaseNode,
  BaseNodeHeader,
  BaseNodeHeaderTitle,
} from '@/components/base-node';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LabeledHandle } from '@/components/labeled-handle';

import { ConfirmDialog } from '@/components/confirm-dialog';
import { Trash2, Pencil } from 'lucide-react';
import { OutputVariableIcon } from '@/components/icons';
import { OutputVariableForm } from './form';
import { TOutputVariableSchema } from '@/types/output-variable';

import { gradientStyle, cn, textColorStyle, defaultHandleStyle } from '@/lib/utils';
import { nodeColors } from '../../constants';
import { useAppStore } from '@/app/admin/store';

export type OutputVariableNode = Node<TOutputVariableSchema>;

export function OutputVariableNode(node: NodeProps<OutputVariableNode>) {
  const { id, data } = node;
  const [onDeleteConfirmOpen, setOnDeleteConfirmOpen] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const { deleteNode } = useAppStore((state) => ({
    deleteNode: state.deleteNode,
  }))


  const [handleStyle, setHandleStyle] = useState<React.CSSProperties>({});

  return (
    <div className="group">
      <div className="flex p-3 space-x-2 justify-center items-center bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Trash2 onClick={() => { setOnDeleteConfirmOpen(true) }} className='w-4 h-4'/>
        <ConfirmDialog
          isOpen={onDeleteConfirmOpen}
          onClose={() => setOnDeleteConfirmOpen(false)}
          title="删除输出变量"
          description="确定要删除此输出变量？"
          onConfirm={() => {deleteNode(id) }}
        />

        <Pencil className='w-4 h-4'/>
      </div>

      <BaseNode onClick={() => setShowForm(true)}
        onMouseEnter={() => {
          setHandleStyle(defaultHandleStyle())
          }}
        onMouseLeave={() => {
          setHandleStyle({})
          } }
      >
        <BaseNodeHeader className={cn("min-w-[240px] items-center justify-between py-2 rounded-md")}
          style={ gradientStyle(nodeColors['input-variable'])  }
        >
          <LabeledHandle title="" type="source" position={Position.Left} 
            style={handleStyle}
          />
          <OutputVariableIcon className={cn("w-4 h-4")}
           style={textColorStyle(nodeColors['output-variable'])}
          />
          <BaseNodeHeaderTitle>{ data.varname || 'output-variable' }</BaseNodeHeaderTitle>
        </BaseNodeHeader>
      </BaseNode>

      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className='flex flex-row items-center'>
              <OutputVariableIcon className="w-4 h-4 mr-2" />
              修改输出变量
            </DialogTitle>
          </DialogHeader>

          <OutputVariableForm {...node} setFormOpen={setShowForm}/>
        </DialogContent>
      </Dialog>
    </div>
  );
}
