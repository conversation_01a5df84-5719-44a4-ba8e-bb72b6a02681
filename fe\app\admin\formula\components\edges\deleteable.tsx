'use client';

import React from 'react';
import {
  BaseEdge,
  EdgeLabelRenderer,
  getSmoothStepPath,
  type EdgeProps,
} from '@xyflow/react';
import { Button } from '@/components/ui/button';

import { useAppStore } from '../../../store';

export function DeleteableEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
}: EdgeProps) {
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const { deleteEdge } = useAppStore((state) => ({
    deleteEdge: (state as { deleteEdge: (id: string) => void }).deleteEdge,
  }));

  return (
    <>
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            pointerEvents: 'all',
            transformOrigin: 'center',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
          }}
        >
          <Button className='border-1 rounded-full shadow-none'
            size='sm'
            variant='outline'
            onClick={ () => { deleteEdge(id)} }>
            ×
          </Button>
        </div>
      </EdgeLabelRenderer>
    </>
  );
}
