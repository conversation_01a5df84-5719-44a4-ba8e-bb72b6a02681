'use client';

import React, { useRef, useMemo } from 'react';
import { LineChart, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { ChartContainer, ChartConfig } from '@/components/ui/chart';
import { TLineWidgetProps, lineWidgetSampleDataSchema } from '@/types/line-widget-props';
import { useParentResizeObserver } from '@/hooks/use-parent-resize';
export type LineRenderProps = {
  id: string
} & TLineWidgetProps;

const chartConfig = {
  desktop: {
    label: "Desktop",
    color: "#2563eb",
  },
  mobile: {
    label: "Mobile",
    color: "#60a5fa",
  },
} satisfies ChartConfig


export function LineRender(
  { lineStrokeWidth, lineStrokeColor, sampleData }: LineRenderProps
) {
  const data = useMemo(() => {
    try {
      return lineWidgetSampleDataSchema.parse(JSON.parse(sampleData));
    }catch(e) {
      console.error("lineWidgetSampleDataSchema error", e);
    }
  }, [sampleData]);

  const ref = useRef<HTMLDivElement>(null);
  const dim = useParentResizeObserver(ref);

  return (
    <ChartContainer ref={ref} config={chartConfig} 
      style={{
        height: dim?.height || 150,
        width: dim?.width || 500,
      }}
    >
      <LineChart
        data={data}
        margin={{
          top: 5,
          right: 10,
          left: 10,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="x" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Line type="monotone" dataKey="y" stroke={lineStrokeColor} strokeWidth={lineStrokeWidth} activeDot={{ r: 8 }} />
      </LineChart>
    </ChartContainer>
  );
}
