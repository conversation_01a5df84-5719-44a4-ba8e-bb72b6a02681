import { nanoid } from 'nanoid';
import { zodSchemaDefaultValues } from '@/lib/utils';
import { schemaMap, TWidgetPropsUnion, TWidgetBase } from '@/types';
import { Layout } from 'react-grid-layout';

export interface SceneStore {
  widgets: (TWidgetBase & { props: TWidgetPropsUnion })[];
  applyLayoutsChange: (layouts: Layout[]) => void;
  addEmptyWidget: (type: string) => void;
  deleteWidget: (id: string) => void;
  updateWidgetProps: (id: string, props: TWidgetPropsUnion) => void;
}


// @ts-expect-error "omit"
export function scene(set, get) {
  return {
    widgets: [],

    addEmptyWidget(type: string) {
      const id = nanoid(6);
      // @ts-expect-error "omit"
      const maxY = get().widgets.reduce((max, widget) => Math.max(max, widget.y + widget.h), 0);
      const pos = {
        x:0,
        y: maxY + 6,
        w: 6,
        h: 6,
      }




      const props = zodSchemaDefaultValues(schemaMap[type])
      const widget = {
        id,
        type,
        ...pos,
        props: props,
      }
      set({ widgets: [widget, ...get().widgets] });
    },

    applyLayoutsChange(layouts: Layout[]) {
      // @ts-expect-error "omit"
      set({widgets: get().widgets.map((widget) => {

        const layout = layouts.find((l) => l.i === widget.id);
        if (layout) { return { ...widget, x: layout.x, y: layout.y, w: layout.w, h: layout.h } }
        return widget;
      }) })
    },

    deleteWidget(id: string) {
      // @ts-expect-error "omit"
      set({ widgets: get().widgets.filter((widget) => widget.id !== id) })
    },

      // @ts-expect-error "omit"
    updateWidgetProps(id, props) {
      // @ts-expect-error "omit"
      set({ widgets: get().widgets.map((widget) => {
        if (widget.id === id) { return { ...widget, props: { ...widget.props, ...props } } }
        return widget;
      })})
    }
  }
}
