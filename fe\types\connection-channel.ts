import { z } from 'zod';

import { variableNameRegex } from '@/lib/utils';

export const connectionChannelTypeValues = ["整形", "浮点型", "字符串"] as const
export type ConnectionChannelType = typeof connectionChannelTypeValues[number];

export const connectionChannelSchema =  z.object({
  sn: z.string().min(1, '设备SN不能为空').max(100, '设备SN不能超过100个字符').default('SN0001'), // 设备SN
  name: z.string().min(1, '通道名称不能为空').max(100, '通道名称不能超过100个字符').default('连接通道1'), // 通道名称
  type: z.enum(connectionChannelTypeValues).default("整形"), // 通道类型
  varname: z.string().regex(variableNameRegex, '变量名只能包含字母、数字、下划线和美元符号').min(1, '变量名不能为空').max(100, '变量名不能超过100个字符').default('var1'), // 变量名
  sampleMode: z.string().default('实时'), // 采样模式
  sampleRate: z.number().min(1, '采样率必须大于0').default(1000), // 采样率
  pendingSample: z.number().min(0, '待处理采样数不能小于0').default(0), // 待处理采样数
  deviceId: z.string().default(""), // 设备ID
  deviceSN: z.string().default(""),
  ai: z.number().default(0),
  sensorId: z.string().default(""), // 传感器ID
  sensorName: z.string().default(""),
})

export type TConnectionChannelSchema = z.infer<typeof connectionChannelSchema>;

