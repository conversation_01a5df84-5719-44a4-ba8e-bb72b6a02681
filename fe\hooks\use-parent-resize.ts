import { RefObject, useEffect, useState } from "react";
export function useParentResizeObserver(ref: RefObject<HTMLDivElement | null>) {
  const [dimensions, setDimensions] = useState<DOMRectReadOnly | null>(null);
 
  useEffect(() => {
    if (!ref.current?.parentElement) return;
 
    const observer = new ResizeObserver((entries) => {
      // 监听父元素而非 ref 自身
      const entry = entries[0];
      setDimensions(entry.contentRect);
    });
 
    observer.observe(ref.current.parentElement); // 观测父元素
 
    return () => {
      observer.disconnect(); // 清除监听
    };
  }, [ref]);
 
  return dimensions;
}
