import React from 'react';
import { NodeProps, useReactFlow } from '@xyflow/react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TOutputVariableSchema, outputVariableSchema } from '@/types/output-variable';
import { Form, FormItem, FormControl, FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { OutputVariableNode } from './node';

export type OutputVariableFormProps = {
  setFormOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const OutputVariableForm: React.FC<NodeProps<OutputVariableNode> & OutputVariableFormProps> = ({
  id, data, setFormOpen
}) => {
  const {setNodes} = useReactFlow();

  const form = useForm<Partial<TOutputVariableSchema>>({
    resolver: zodResolver(outputVariableSchema),
    defaultValues: {
      ...{
        varname: 'output1',
        description: '',
      },
      ...data,
    }
  });

  const onSubmit = (data: Partial<TOutputVariableSchema>) => {
    setNodes((nodes) => {
      return nodes.map((node) => {
        if (node.id === id) { return { ...node, data } }
        return node
      })
    })

    setFormOpen(false);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-2 p-2"
      >
        <FormField
          control={form.control}
          name="varname"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>变量名</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'/>
                </FormControl>
              </div>
              <FormMessage></FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({field}) => (
            <FormItem className="flex flex-col">
              <div className="flex items-center justify-between">
                <FormLabel className='basis-1/3'>描述</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} />
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.description ? form.formState.errors.description.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />


        <Button type="submit" className='mt-4 cursor-pointer' disabled={form.formState.isSubmitting} >提交</Button>
      </form>
    </Form>
  );
};
