import React from 'react';
import { NodeProps, useReactFlow } from '@xyflow/react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TFunctionLibrarySchema, functionLibrarySchema } from '@/types/function-library';
import { Form, FormItem, FormControl, FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FunctionLibraryNode } from './node'
import Editor from '@monaco-editor/react';


export type FunctionLibraryFormProps = {
  setFormOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const FunctionLibraryForm: React.FC<NodeProps<FunctionLibraryNode> & FunctionLibraryFormProps> = ({
  id, data, setFormOpen
}) => {
  const {setNodes} = useReactFlow();

  const form = useForm<Partial<TFunctionLibrarySchema>>({
    resolver: zod<PERSON>esolver(functionLibrarySchema),
    defaultValues: { ...{ name: '新自定义函数库', code: '' }, ...data, }
  });

  const onSubmit = (data: Partial<TFunctionLibrarySchema>) => {
    setNodes((nodes) => {
      return nodes.map((node) => {
        if (node.id === id) { return { ...node, data } }
        return node
      })
    })

    setFormOpen(false);
  }

  // Watch the 'code' field
  const codeValue = form.watch("code");

  // Handle editor changes
  const handleEditorChange = (value?: string) => {
    form.setValue("code", value); // Update form field
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-2 p-2"
      >
        <FormField
          control={form.control}
          name="name"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>名称</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>

              <FormMessage className='text-right'>
                {
                  form.formState.errors.name ? form.formState.errors.name.message : ''
                }
              </FormMessage>
            </FormItem>

          )}
        />


        <FormField
          control={form.control}
          name="code"
          render={() => (
            <FormItem className="flex flex-col">
              <div className="flex items-center justify-between">
                <FormLabel className='basis-1/3'>代码</FormLabel>
                <Editor
                  height="300px"
                  defaultLanguage="python"
                  value={codeValue}
                  onChange={handleEditorChange}
                  options={{
                    minimap: { enabled: false },
                    fontSize: 14,
                    wordWrap: "on",
                    lineNumbers: "off",
                  }}
                />

                {/* Hidden input for form submission */}
                <input type="hidden" {...form.register("code", { required: "Code is required" })} />
                {form.formState.errors.code && <p className="text-red-500">{form.formState.errors.code.message}</p>}
              </div>

              <FormMessage className='text-right'>
                {
                  form.formState.errors.code ? form.formState.errors.code.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />

        <Button type="submit" className='mt-4 cursor-pointer' disabled={form.formState.isSubmitting} >提交</Button>
      </form>
    </Form>
  );
};
