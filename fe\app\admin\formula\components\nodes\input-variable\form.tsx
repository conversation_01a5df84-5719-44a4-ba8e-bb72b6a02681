import React from 'react';
import { NodeProps, useReactFlow } from '@xyflow/react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { TInputVariableSchema, inputVariableSchema, inputVariableTypeValues } from '@/types/input-variable';
import { Form, FormItem, FormControl, FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectItem, SelectContent } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { InputVariableNode } from './node'

export type InputVariableFormProps = {
  setFormOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const InputVariableForm: React.FC<NodeProps<InputVariableNode> & InputVariableFormProps> = ({
  id, data, setFormOpen
}) => {
  const {setNodes} = useReactFlow();

  const form = useForm<Partial<TInputVariableSchema>>({
    resolver: zodResolver(inputVariableSchema),
    defaultValues: {
      ...{
        name: '输入变量',
        type: '整形',
        value: '',
        description: '',
      },
      ...data,
    }
  });

  const onSubmit = (data: Partial<TInputVariableSchema>) => {
    setNodes((nodes) => {
      return nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data
          }
        }

        return node
      })
    })

    setFormOpen(false);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-2 p-2"
      >
        <FormField
          control={form.control}
          name="name"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>名称</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>

              <FormMessage className='text-right'>
                {
                  form.formState.errors.name ? form.formState.errors.name.message : ''
                }
              </FormMessage>

            </FormItem>

          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>类型</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger className='col-span-3 w-full'>
                      <SelectValue placeholder="选择数据类型" />
                    </SelectTrigger>

                    <SelectContent>
                      { inputVariableTypeValues.map((type) => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      )) }
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.type ? form.formState.errors.type.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="varname"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>变量名</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'/>
                </FormControl>
              </div>
              <FormMessage></FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="value"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>值</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'/>
                </FormControl>
              </div>
              <FormMessage></FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({field}) => (
            <FormItem className="flex flex-col">
              <div className="flex items-center justify-between">
                <FormLabel className='basis-1/3'>描述</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} />
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.description ? form.formState.errors.description.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />


        <Button type="submit" className='mt-4 cursor-pointer' disabled={form.formState.isSubmitting} >提交</Button>
      </form>
    </Form>
  );
};
