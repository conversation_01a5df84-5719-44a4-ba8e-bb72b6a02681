'use client';

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { useRouter } from "next/navigation";

import { Plus, Trash2, Pencil } from "lucide-react";

export default function Page() {
  const router = useRouter();

  const gotoScene = () => {
    router.push('/admin/monitor/scene')
  }

  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex flex-row gap-4 justify-between items-center h-full">
        <div>场景 - 场景列表</div>
        <Button className='cursor-pointer rounded-none'>新建场景</Button>
      </div>

      <div className="grid grid-cols-5 md:grid-cols-5 lg:grid-cols-5 sm:grid-cols-2 gap-8">
        <Card className="shadow-lg rounded-none cursor-pointer justify-center items-center flex hover:transform hover:scale-105 transition-transform duration-300  border-t-4  border-primary border-b-0 border-x-0"
          onClick={gotoScene}
        >
          <Plus className="w-6 h-6 text-gray-500" />
          <div>点击创建/复制场景</div>
        </Card>

        <Card className="shadow-lg rounded-none cursor-pointer hover:transform hover:scale-105 transition-transform duration-300 border-t-4  border-primary border-b-0 border-x-0">
          <CardHeader className=''>
            <CardTitle className='text-primary'>场景 4</CardTitle>
            <CardDescription>BTT单机风扇观测2025/03</CardDescription>
          </CardHeader>
          <CardContent>
            <p>用户行为分析之漏斗分析</p>
          </CardContent>
          <CardFooter className="flex flex-row items-center justify-between space-x-2">
            <div className='text-gray-400'>图表数量: 10</div>
            <div className="flex space-x-2">
              <Trash2 className="w-4 h-4" />
              <Pencil className='w-4 h-4' 
                onClick={gotoScene}
              />
            </div>
          </CardFooter>
        </Card>

        <Card className="shadow-lg rounded-none cursor-pointer hover:transform hover:scale-105 transition-transform duration-300  border-t-4  border-primary border-b-0 border-x-0">
          <CardHeader>
            <CardTitle>场景 5</CardTitle>
            <CardDescription>BTT单机风扇观测2025/03</CardDescription>
          </CardHeader>
          <CardContent>
            <p>用户行为分析之漏斗分析</p>
          </CardContent>
          <CardFooter className="flex flex-row items-center justify-between space-x-2">
            <div className='text-gray-400'>图表数量: 10</div>
            <div className="flex space-x-2">
              <Trash2 className="w-4 h-4" />
              <Pencil className='w-4 h-4' 
                onClick={gotoScene}
              />
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
