'use client';

import React from 'react';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ConfirmDialog } from '@/components/confirm-dialog';
import { EditWidgetForm } from '@/app/admin/monitor/scene/components/edit-widget-form';

import { Ellipsis } from 'lucide-react';
import { useAppStore } from '@/app/admin/store';

export type WidgetWrapperProps = {
  id: string;
  title?: string;
}

export function WidgetWrapper({ children, title, id }: { children: React.ReactNode } & WidgetWrapperProps) {
  const { deleteWidget } = useAppStore((state) => ({
    deleteWidget: state.deleteWidget,
  }))
  const { openDialog, closeDialog } = useAppStore.getState();

  const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
  const [isEditWidgetFormOpen, setIsEditWidgetFormOpen] = React.useState(false);
  const onDeleteWidget = () => {
    deleteWidget(id);
    closeDialog(`${id}-delete-confirm`)
    setShowDeleteConfirm(false);
  }

  return (
    <Card className='rounded-none shadow-sm hover:transform 
      w-full h-full overflow-hidden bg-white p-2'
    >
      <CardHeader className='p-0'>
        <CardTitle>{ title }</CardTitle>
        <CardAction>
          <DropdownMenu>
            <DropdownMenuTrigger>
              <Ellipsis className="" />
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>{ title }</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => {
                openDialog(`${id}`)
                setIsEditWidgetFormOpen(true)
              }
              }>编辑</DropdownMenuItem>

              <DropdownMenuItem onClick={() => {
                openDialog(`${id}-delete-confirm`)
                setShowDeleteConfirm(true)
              }
              }>删除</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <ConfirmDialog
            isOpen={showDeleteConfirm}
            onClose={() => setShowDeleteConfirm(false)}
            title="删除当前图表"
            description="确定要删除此图表？"
            onConfirm={ onDeleteWidget }
          />

          <EditWidgetForm id={id} isOpen={isEditWidgetFormOpen} setIsOpen={setIsEditWidgetFormOpen} />
        </CardAction>
      </CardHeader>
      <CardContent className='h-[calc(100%-3rem)] p-2'>
        { children }
      </CardContent>
      <CardFooter>
      </CardFooter>
    </Card>
  )
}
