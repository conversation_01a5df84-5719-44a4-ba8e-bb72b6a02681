import { Position } from '@xyflow/react';

import { ConnectionChannelNode } from './nodes/connection-channel';
import { InputVariableNode } from './nodes/input-variable';
import { OutputVariableNode } from './nodes/output-variable';
import { FunctionLibraryNode } from './nodes/function-library';
import { SensorNode } from './nodes/sensor';
import { DeviceNode } from './nodes/device';
import { TargetNode } from './nodes/target';
import { FormulaNode } from './nodes/formula';

import { SensorForm } from './nodes/sensor';
import { ConnectionChannelForm } from './nodes/connection-channel';
import { InputVariableForm } from './nodes/input-variable';
import { OutputVariableForm } from './nodes/output-variable';
import { TargetForm } from './nodes/target';
import { FormulaForm } from './nodes/formula';
import { FunctionLibraryForm } from './nodes/function-library';

import { DeleteableEdge } from './edges/deleteable';

export type NodeType = 'sensor' | 'device' | 'connection-channel' | 'target' | 'input-variable' | 'output-variable' | 'function-library' | 'formula';

export const nodeTypes = {
  sensor: SensorNode,
  device: DeviceNode,
  'connection-channel': ConnectionChannelNode,
  target: TargetNode,
  formula: FormulaNode,
  'input-variable': InputVariableNode,
  'output-variable': OutputVariableNode,
  'function-library': FunctionLibraryNode,
};

export const edgeTypes = {
  'deleteable': DeleteableEdge,
}

export const nodeFormTypes = {
  sensor: SensorForm,
  'connection-channel': ConnectionChannelForm,
  target: TargetForm,
  'input-variable': InputVariableForm,
  'output-variable': OutputVariableForm,
  'formula': FormulaForm,
  'function-library': FunctionLibraryForm,
}

export const nodeNames = {
  sensor: '传感器',
  device: '板卡设备',
  'connection-channel': '连接通道',
  target: '测量模型',
  formula: '公式',
  'input-variable': '输入变量',
  'output-variable': '输出变量',
  'function-library': '自定义函数库',
}

export const validConnectionRoutes = {
  sensor: ['device'],
  device: ['connection-channel', 'sensor'],
  'connection-channel': ['device', 'formula'],
  'input-variable': ['formula'],
  'output-variable': ['formula'],
  target: ['formula'],
  formula: ['target', 'function-library', 'input-variable', 'output-variable'],
  'function-library': ['formula'],
}

export const nodeIcons = {
  sensor: "Thermometer",
  device: "Camera",
  'connection-channel': "ChevronsLeftRightEllipsis",
  target: "Target",
  formula: "BookText",
  'input-variable': "ArrowRight",
  'output-variable': "ArrowLeft",
  'function-library': "Warehouse",
}

  // --sensor: "#3B82F6";
  // --device: "#10B981";
  // --connection-channel: "#8B5CF6";
  // --target: "#EF4444";
  // --input-variable: "#F59E0B";
  // --output-variable: "#EC4899";
  // --function-library: "#06B6D4";
  // --formula: "#84CC16";
export const nodeColors = {
  sensor: '#38BDF8',
  'connection-channel': '#6B5CF6',
  device: '#10B981',
  target: '#CFC444',
  formula: '#84CC16',
  'input-variable': '#D59E0B',
  'output-variable': '#F59E0B',
  'function-library': '#06B6D4',
}

export const nodeSourcePosition = {
  sensor: Position.Right,
  device: null,
  'connection-channel': Position.Right,
  target: Position.Right,
  formula: null,
  'input-variable': Position.Right,
  'output-variable': Position.Left,
  'function-library': Position.Right,
}

export const nodeTargetPosition = {
  sensor: null,
  device: Position.Left,
  'connection-channel': Position.Left,
  target: null,
  formula: Position.Left,
  'input-variable': null,
  'output-variable': null,
  'function-library': null,
}

export {
  ConnectionChannelNode,
  FormulaNode,
  InputVariableNode,
  OutputVariableNode,
  FunctionLibraryNode,
  SensorNode,
  DeviceNode,
  TargetNode,
}
