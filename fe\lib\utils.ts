import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { z } from "zod"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function randid() {
  return Math.random().toString(36).slice(2, 10)
}

export function zodSchemaDefaultValues<Schema extends z.ZodTypeAny>(schema: Schema): z.infer<Schema> {
  return schema.parse({});
}

export function toHexString(num: number): string {
  return "0x" + num.toString(16).padStart(2, '0').toUpperCase();
}


export const variableNameRegex = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/;

export function shortStringInMiddle(name: string): string {
  if (name.length <= 10) return name;
  return name.slice(0, 5) + '...' + name.slice(-5);
}

export function shortStringInEnd(name: string): string {
  if (name.length <= 8) return name;
  return name.slice(0, 8) + '...';
}

export function textColorStyle(baseColor: string): React.CSSProperties {
  const color = baseColor.toLowerCase();
  return {
    color: color
  }
}

export function gradientStyle(baseColor: string): React.CSSProperties {
  const fromColor = lightenByMixingWhite(baseColor, 90) ;
  const toColor = lightenByMixingWhite(baseColor, 99) ;
  return {
    backgroundImage: `linear-gradient(to bottom, ${fromColor}, ${toColor})`,
    fontWeight: 'bold',
  }
}

export function defaultHandleStyle(): React.CSSProperties {
  return {
    width: '14px',
    height: '14px',
    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)',
    backgroundColor: 'blue',
    borderColor: 'white',
    borderWidth: '2px',
    transition: 'height 0.3s ease, width 0.3s ease, text-shadow 0.3s ease-in-out',
  }
}

function lightenByMixingWhite(hex: string, percent: number) {
  const ratio = percent / 100;
  const white = 0xff * ratio; // 白色分量
  const keep = 1 - ratio;     // 原色保留比例

  const r = Math.round(parseInt(hex.substring(1, 3), 16) * keep + white);
  const g = Math.round(parseInt(hex.substring(3, 5), 16) * keep + white);
  const b = Math.round(parseInt(hex.substring(5, 7), 16) * keep + white);

  return `#${
r.toString(16).padStart(2, '0')}${
g.toString(16).padStart(2, '0')}${
b.toString(16).padStart(2, '0')}`;
}

export function isAnyDialogOpen(){
  return document.querySelectorAll('[data-state="open"][role="dialog"]').length > 0;
}
