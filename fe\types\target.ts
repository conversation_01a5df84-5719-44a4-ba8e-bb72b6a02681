import { z } from 'zod';

export const targetTypeValues = ['键相1/R', '键相m/R', '转子叶盘', '其他'] as const
export type TargetType = typeof targetTypeValues[number];

export const targetSchema = z.object({
  id: z.string().uuid().default(() => crypto.randomUUID()),
  name: z.string().min(1, '模型名称不能为空').max(100, '模型名称不能超过100个字符').default('测量模型'),
  type: z.enum(targetTypeValues).default("键相1/R"),
  measurePt: z.string().default(''),
})

export type TTargetSchema = z.infer<typeof targetSchema>;
