'use client';

import React, { useMemo, useRef } from 'react';

import { BarChart, Bar, Rectangle, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend } from 'recharts';

import { ChartContainer, ChartConfig } from '@/components/ui/chart';

import { TBarWidgetProps, barWidgetSampleDataSchema } from '@/types/bar-widget-props';
import { useParentResizeObserver } from '@/hooks/use-parent-resize';
export type BarRenderProps = {
  id: string
} & TBarWidgetProps;

const chartConfig = {
  desktop: {
    label: "Desktop",
    color: "#2563eb",
  },
  mobile: {
    label: "Mobile",
    color: "#60a5fa",
  },
} satisfies ChartConfig

export function BarRender( { sampleData, barColor }: BarRenderProps) {
  const data = useMemo(() => {
    try {
      return barWidgetSampleDataSchema.parse(JSON.parse(sampleData));
    }catch(e) {
      console.error("barWidgetSampleDataSchema error", e);
    }
  }, [sampleData]);

  const ref = useRef<HTMLDivElement>(null);
  const dim = useParentResizeObserver(ref);

  return (
    <ChartContainer ref={ref} config={chartConfig}
      style={{
        height: dim?.height || 150,
        width: dim?.width || 500,
      }}>
      <BarChart
        data={data}
        margin={{
          top: 5,
          right: 10,
          left: 10,
          bottom: 5,
        }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="x" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar dataKey="y" fill={barColor} activeBar={<Rectangle fill="pink" stroke="blue" />} />
      </BarChart>
    </ChartContainer>
  );
}
