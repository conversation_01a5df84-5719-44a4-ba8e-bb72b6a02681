## 项目与 UI 技术栈概览

本前端为 Next.js 15（App Router）+ React 19 + TypeScript 应用，采用 Tailwind CSS v4 作为样式方案，结合 Radix UI 原子组件与 shadcn/ui 的样式/模式，配合 Zustand 做客户端状态管理，axios 负责 HTTP 调用，内置 API 代理路由与可视化/编辑类库（XYFlow、Recharts、react-grid-layout、Monaco）。

- 运行脚本（package.json）
  - dev: next dev --turbopack（开发）
  - build: next build（构建）
  - start: next start（启动）
  - lint: next lint（ESLint）
- 关键依赖
  - 框架与运行时：next@15、react@19、react-dom@19、TypeScript
  - 样式：tailwindcss@^4、@tailwindcss/postcss、tw-animate-css、clsx、tailwind-merge
  - 组件/交互：@radix-ui/*、shadcn/ui（通过 components/ui/* 封装）、lucide-react 图标、sonner 通知、next-themes 主题
  - 状态管理：zustand（含 persist 中间件）
  - 表单与校验：react-hook-form、zod、@hookform/resolvers
  - 数据请求：axios（lib/api.ts）
  - 可视化与编辑：@xyflow/react（React Flow）、recharts、react-grid-layout、@monaco-editor/react、react-colorful
  - 工具：usehooks-ts、nanoid 等
- 配置与构建
  - next.config.ts：output=standalone（便于 Docker/独立运行）
  - postcss.config.mjs：启用 @tailwindcss/postcss 插件
  - tsconfig.json：paths 使用 @/* 别名
  - eslint.config.mjs：next/core-web-vitals + typescript 规则
  - Dockerfile：多阶段构建（deps/builder/runner），Node 18-alpine 基础镜像

## 代码结构（UI 相关）

- app/（Next.js App Router 路由目录）
  - layout.tsx：根布局，注册 Google 字体（Geist/Geist Mono），引入全局样式 app/globals.css，并设置 metadata
  - globals.css：Tailwind v4 入口与主题 Token（CSS 变量），dark 变体、边框/背景/前景色等设计系统变量
  - api/proxy/[...path]/route.js：API 代理路由（GET/POST/PUT/PATCH/DELETE），将请求转发至 BACKEND_URL
  - playground/：实验页（page.tsx、layout.tsx），示例调用 lib/api.ts 的 healthCheck 并演示 UI Button
  - admin/：管理端主要界面与子路由
    - layout.tsx：管理端布局
    - components/、devices/、formula/、history/、monitor/、users/、utilities/：功能域子模块
    - formula/layout.tsx：对公式/流程编辑模块包裹 ReactFlowProvider，并渲染全局 Toaster
    - store/：Zustand 组合 Store（见下文）

- components/（UI 组件与领域组件）
  - ui/：基于 Radix/shadcn 模式的可复用 UI 原子组件
    - alert-dialog、avatar、breadcrumb、button、card、chart、collapsible、dialog、dropdown-menu、form、input、label、popover、select、separator、sheet、sidebar、skeleton、sonner、table、tabs、textarea、tooltip 等
    - 特别说明：components/ui/sonner.tsx 使用 next-themes 的 useTheme，与 Sonner 结合实现主题联动通知
  - icons/：图标索引（lucide-react）
  - 领域组件与编辑节点
    - base-node、base-handle、labeled-handle、data-edge：与 @xyflow/react 配合的节点/连线基础组件
    - app-sidebar、nav-main、nav-user、team-switcher：导航侧栏/头部等业务 UI
    - color-picker、confirm-dialog、list-info：通用交互模块

- lib/
  - api.ts：axios 实例与示例请求（healthCheck）
  - utils.ts：
    - cn：className 合并（clsx + tailwind-merge）
    - zodSchemaDefaultValues：从 zod schema 生成默认值
    - 其他工具：randid、toHexString、变量名正则、短字符串格式化等

- app/admin/store/
  - index.ts：组合 store（createWithEqualityFn + persist），持久化 key：formula-store
    - 组合切片：formula、scene、dialogTracker（分别从同名文件导入）

- 根配置
  - components.json：shadcn/ui 配置，style: new-york，iconLibrary: lucide，路径别名（components/ui/lib/hooks 等映射到 @/*）
  - next.config.ts、postcss.config.mjs、tsconfig.json、eslint.config.mjs、Dockerfile

## 样式与设计系统

- Tailwind v4（以 CSS 导入为主）：
  - app/globals.css 顶部 @import "tailwindcss" 与 "tw-animate-css"
  - 通过 CSS 变量集中定义主题 Token（如 --background、--foreground、--primary 等）
  - 自定义 dark 变体：@custom-variant dark (&:is(.dark *)); 支持基于 .dark 类的深色模式
  - @layer base 为 * 与 body 注入基础样式（边框/轮廓、背景/字体色）
- 字体：next/font/google 引入 Geist 与 Geist Mono，并以 CSS 变量注入到 Tailwind 的 --font-sans/--font-mono
- 主题切换：next-themes 已在 Toaster 中使用，可在上层布局或 providers 中集成 ThemeProvider 实现系统/手动主题

## 路由与渲染约定

- App Router（app/ 目录），支持 RSC 与 CSR 混合：
  - 客户端组件需加 "use client"（如 playground/page.tsx、components/ui/sonner.tsx）
  - 统一在 app/layout.tsx 中输出 <html>/<body> 与全局样式
  - 子路由可定义自己的 layout.tsx（如 app/admin/layout.tsx、app/admin/formula/layout.tsx）

## 状态管理（Zustand）

- 组合 Store：app/admin/store/index.ts
  - createWithEqualityFn + persist（localStorage）
  - 切片：formula、scene、dialogTracker（类型分别为 FormulaStore、SceneStore、DialogTrackerStore）
  - 使用建议：从 useAppStore(selector, shallowEqual?) 读取所需片段；注意持久化 key 冲突与结构演进（需迁移策略）

## 表单与校验

- 表单：react-hook-form
- 校验：zod + @hookform/resolvers
- 组件：components/ui/form.tsx、input.tsx、select.tsx、textarea.tsx、label.tsx 等
- 建议：
  - 使用 zodResolver 与 utils.ts 中 zodSchemaDefaultValues 统一默认值/类型
  - 按需用 shadcn UI 包装输入控件，复用样式与状态提示

## 数据请求与 API 代理

- axios 实例（lib/api.ts）：设置 Accept/Content-Type: application/json
- 示例：healthCheck() 调用 /api/proxy/health（GET/POST/DELETE/PUT）并在控制台输出
- 代理路由：app/api/proxy/[...path]/route.js
  - BACKEND_URL 环境变量控制后端地址
  - 保留原查询参数、方法与请求头（修正 host），在 4xx/5xx 时返回错误
  - 注意：当前实现默认 json() 响应；如后端返回非 JSON，需按需转发流/文本

## 可视化/编辑类库集成

- @xyflow/react（React Flow）：流程/公式编辑
  - 在 app/admin/formula/layout.tsx 以 ReactFlowProvider 提供上下文
  - 自定义节点/连线组件位于 components/*（base-node、labeled-handle、data-edge 等）
- recharts：图表渲染（components/ui/chart.tsx 提供封装）
- react-grid-layout：自定义布局/拖拽网格
- @monaco-editor/react：代码/表达式编辑器

## 组件与别名约定

- 别名：@/* -> 项目根（tsconfig.json）
  - 常用映射（见 components.json）：
    - components: @/components
    - ui: @/components/ui
    - lib: @/lib
    - hooks: @/hooks
- 组件放置
  - 原子/通用组件：components/ui/*（遵循 shadcn 风格，基于 Radix primitives 包装）
  - 领域组件：components/* 根下按功能分组（导航、节点、对话框等）
  - 工具函数：lib/utils.ts（cn 等）

## 开发协作建议

- 新增 UI 组件
  - 优先放在 components/ui 下（可复用原子组件），领域专用组件放在 components 根下相应子目录
  - 使用 cn() 合并 className；尽量通过 CSS 变量与 Tailwind Token 继承主题
  - 与 Radix 组件交互时，统一用 shadcn 模式（受控/无障碍属性保持一致）
- 路由/布局
  - 在 app/**/layout.tsx 提供上下文（Provider、Toaster、FlowProvider 等）而非在 page.tsx 中零散引入
- 表单/校验
  - 统一使用 zod + RHF；错误提示和禁用状态用 shadcn 的表单/输入组件实现一致体验
- 状态持久化
  - 当前 persist key 为 formula-store；如拆分模块，建议按域拆分持久化 key 并规划迁移
- API 与代理
  - 统一通过 lib/api.ts 封装 axios；避免在组件中直接 new axios
  - 如需上传/下载二进制，扩展代理路由以支持 stream/Blob

## 运行与构建

- 本地开发：npm run dev（或兼容使用 pnpm/yarn）
- 构建：npm run build
- 启动：npm run start（依赖 .next 产物）
- Docker：多阶段构建（deps->builder->runner），产物使用 standalone 输出，镜像入口 node server

## 待补充/注意事项

- 主题 Provider：若需要系统/手动主题切换，建议在根布局或 providers 中加入 ThemeProvider（next-themes）并同步 .dark 类
- 代理返回格式：当前默认解析为 JSON，如对接文件流/图片，需要条件转发 response.body
- Tailwind v4 配置：采用“零配置 + CSS 入口”模式；如需自定义插件/预设，可扩展 PostCSS/Tailwind 配置

