import { type Node, type NodeProps, Position } from '@xyflow/react';
import { useState } from 'react';

import {
  BaseNode,
  BaseNodeContent,
  BaseNodeFooter,
  BaseNodeHeader,
  BaseNodeHeaderTitle,
} from '@/components/base-node';
import { LabeledHandle } from '@/components/labeled-handle';

import { ListInfo } from '@/components/list-info';
import { CircleDot } from 'lucide-react';
import { TDeviceSchema } from '@/types/device';
import { cn, toHexString, textColorStyle, gradientStyle, defaultHandleStyle } from '@/lib/utils';
import { DeviceIcon } from '@/components/icons';
import { nodeColors } from '../../constants';

export type DeviceNode = Node<TDeviceSchema>

export function DeviceNode({ id, data }: NodeProps<DeviceNode>) {
  const oddHandles = () => {
    return Array.from({ length: data.chNum }).map((_, index) => {
      if (index % 2 !== 0) return null; // Skip even indices

      return (<LabeledHandle key={`${id}-cn-${index}`}
        id={`${id}-ai-${index}`}
        className='py-1'
        title={toHexString(index + 1)}
        type="target"
        position={Position.Left}
        style={handleStyle}
      />)
    })
  }

  const evenHandles = () => {
    return Array.from({ length: data.chNum }).map((_, index) => {
      if (index % 2 === 0) return null; // Skip even indices

      return (<LabeledHandle key={`${id}-cn-${index}`}
        id={`${id}-ai-${index}`}
        className='py-1'
        title={toHexString(index + 1)}
        type="target"
        position={Position.Right}
        style={handleStyle}
      />)
    })
  }

  const rendAttributes = (data: TDeviceSchema) => {
    const attributes = [
      {key: 'SN', value: data.sn || '未知SN'},
      { key: '名称', value: data.name || '未知名称' },
      { key: '型号', value: data.model || '未知型号' },
      { key: '采样率', value: data.sampleRate || '未知采样率' },
      { key: '通道数', value: data.chNum || '未知通道数' }
    ]

    return <ListInfo items={attributes} />
  }

  const [handleStyle, setHandleStyle] = useState<React.CSSProperties>({})

  return (
    <BaseNode
        onMouseEnter={() => {
          setHandleStyle(defaultHandleStyle())
        }}
        onMouseLeave={() => {
          setHandleStyle({})
        }}>
      <BaseNodeHeader className="border-b min-w-[150px] rounded-md"
          style={gradientStyle(nodeColors['device'])} >
        <BaseNodeHeaderTitle
          className={ cn('flex items-center justify-between') }
        >
          <DeviceIcon className={cn("w-4 h-4 ") }
          style={textColorStyle(nodeColors['device']) }
          />
          <span>
            { data.name || '板卡设备' }
            <span className="text-xs text-gray-500 pl-2">
              SN: { data.sn || "SN"}
            </span>
          </span>

          <CircleDot className="w-4 h-4 inline-block text-green-600/90 justify-end" />
        </BaseNodeHeaderTitle>
      </BaseNodeHeader>

      <BaseNodeContent className='grid grid-cols-4 gap-2 p-2'>
        <div>
          { oddHandles() }
        </div>

        <div className={cn("col-span-2",  `row-span-${ data.chNum /2}`)}>
          { rendAttributes(data) }
        </div>

        <div>
          { evenHandles() }
        </div>
      </BaseNodeContent>

      <BaseNodeFooter className="bg-gray-100 px-0 py-1 w-full rounded-b-md">
        实验室 A
      </BaseNodeFooter>
    </BaseNode>
  );
}
