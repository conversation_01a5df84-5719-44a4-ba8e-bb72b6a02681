import React from 'react';
import { NodeProps, useReactFlow } from '@xyflow/react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { sensorSchema, TSensorSchema, sensorTypeValues, formulaTypeValues } from '@/types/sensor';
import { Form, FormItem, FormControl, FormMessage, FormLabel, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectItem, SelectContent } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { SensorNode } from './node'

export type SensorFormProps = {
  setFormOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const SensorForm: React.FC<NodeProps<SensorNode> & SensorFormProps> = ({
  id, data, setFormOpen
}) => {
  const {setNodes} = useReactFlow();

  const form = useForm<Partial<TSensorSchema>>({
    resolver: zodResolver(sensorSchema),
    defaultValues: {
      ...{
        model: '',
        description: '',
        formula: '',
        formulaUnit: '',
      },
      ...data,
    }
  });

  const onSubmit = (data: Partial<TSensorSchema>) => {
    setNodes((nodes) => {
      return nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data
          }
        }

        return node
      })
    })

    setFormOpen(false);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-2 p-2"
      >
        <FormField
          control={form.control}
          name="name"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>名称</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>

              <FormMessage className='text-right'>
                {
                  form.formState.errors.name ? form.formState.errors.name.message : ''
                }
              </FormMessage>

            </FormItem>

          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>类型</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger className='col-span-3 w-full'>
                      <SelectValue placeholder="选择传感器类型" />
                    </SelectTrigger>

                    <SelectContent>
                      { sensorTypeValues.map((type) => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      )) }
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.type ? form.formState.errors.type.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="model"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>型号</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'/>
                </FormControl>
              </div>
              <FormMessage></FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="formulaType"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>公式类型</FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger className='col-span-3 w-full'>
                      <SelectValue placeholder="选择公式类型" />
                    </SelectTrigger>

                    <SelectContent>
                      { formulaTypeValues.map((type) => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      )) }
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.model ? form.formState.errors.model.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="formula"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>公式</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3' />
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.formula ? form.formState.errors.formula.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="formulaUnit"
          render={({field}) => (
            <FormItem>
              <div className="grid grid-cols-4 items-center gap-2">
                <FormLabel className='col-span-1'>公式单位</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} className='col-span-3'/>
                </FormControl>
              </div>
              <FormMessage></FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({field}) => (
            <FormItem className="flex flex-col">
              <div className="flex items-center justify-between">
                <FormLabel className='basis-1/3'>描述</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} />
                </FormControl>
              </div>
              <FormMessage className='text-right'>
                {
                  form.formState.errors.description ? form.formState.errors.description.message : ''
                }
              </FormMessage>
            </FormItem>
          )}
        />


        <Button type="submit" className='mt-4 cursor-pointer' disabled={form.formState.isSubmitting} >提交</Button>
      </form>
    </Form>
  );
};
