import { applyNodeChanges, applyEdgeChanges,
  Position,  MarkerType, 
  Node, NodeChange, Edge, EdgeChange, Connection,
  IsValidConnection
} from '@xyflow/react';
import { nanoid } from 'nanoid';
import { zodSchemaDefaultValues } from '@/lib/utils';
import { schemaMap, TEntitySchemaUnion } from '@/types';
import { TDeviceSchema } from '@/types/device';
import { nodeTargetPosition, nodeSourcePosition } from '../formula/components/constants';
import { toast } from 'sonner';
import { nodeNames, validConnectionRoutes, nodeColors } from '../formula/components/constants';

export interface FormulaStore {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  addEdge: (data: Edge) => void;
  deleteEdge: (id: string) => void;
  deleteNode: (id: string) => void;
  clearNodesAndEdges: () => void;
  isValidConnection: IsValidConnection<Edge>;
  onConnect: (event: Connection) => void;
  addDeviceNodeIfNotExist: (data: TDeviceSchema) => void;
  addNewNode: (type: string) => Node;
  updateNodeData: (nodeId: string, data: TEntitySchemaUnion ) => void;
}

// @ts-expect-error "omit"
export function formula(set, get) {
  return {
    nodes: [],
    edges: [],
    onNodesChange(changes: NodeChange[]) { set({ nodes: applyNodeChanges(changes, get().nodes) }); },
    onEdgesChange(changes: EdgeChange[]) { set({ edges: applyEdgeChanges(changes, get().edges) }); },


    addEdge(data: Edge) {
      // @ts-expect-error "omit"
      const edge = { id: nanoid(6), ...data };

      set({ edges: [edge, ...get().edges] });
    },

    // @ts-expect-error "omit"
    deleteEdge(id: string) { set({ edges: get().edges.filter((edge) => edge.id !== id) }) },

    deleteNode(id: string) {
      const edges = get().edges.filter((edge: Edge) => edge.source === id || edge.target === id);
      set({ edges: get().edges.filter((edge: Edge) => !edges.includes(edge)) })
      set({ nodes: get().nodes.filter((node: Node) => node.id !== id) })
    },

    clearNodesAndEdges() { set({ edges: [], nodes: [] }); },

    // @ts-expect-error "omit"
    isValidConnection(connection) {
      const { source, target, sourceHandle, targetHandle } = connection;
      const sourceNodeType = get().nodes.find((node: Node) => node.id === source)?.type || "";
      const targetNodeType = get().nodes.find((node: Node) => node.id === target)?.type || "";

      // @ts-expect-error "omit"
      if(!validConnectionRoutes[sourceNodeType]?.includes(targetNodeType)){
        // @ts-expect-error "omit"
        console.error(`从${nodeNames[sourceNodeType]}到${nodeNames[targetNodeType]}的连接不符合规则`);
        // @ts-expect-error "omit"
        toast.error(`从${nodeNames[sourceNodeType]}到${nodeNames[targetNodeType]}的连接不符合规则`);
        return false;
      }

      if(sourceHandle && !sourceHandle.includes(targetNodeType)) {

        // @ts-expect-error "omit"
        console.log(`源节点${nodeNames[sourceNodeType]}的连接点${sourceHandle}不允许连接到目标节点${nodeNames[targetNodeType]}的连接点${targetHandle}`);
        return false;
      }

      const edgesFromSourceHandle = get().edges.filter((edge: Edge) => edge.source === source && edge.sourceHandle === sourceHandle);
      if (edgesFromSourceHandle.length > 0) {
        // @ts-expect-error "omit"
        console.log(`源节点${nodeNames[sourceNodeType]}的连接点${sourceHandle}已经有连接，不能重复连接`);
        return false;
      }

      return true
    },

    // @ts-expect-error "omit"
    onConnect(event) {
      const { source, target, sourceHandle, targetHandle } = event;
      const sourceNodeType = get().nodes.find((node: Node) => node.id === source)?.type || "";
      // @ts-expect-error "omit"
      const sourceColor = nodeColors[sourceNodeType] || '#FF0072';
      const style= {
        strokeWidth: 2,
        stroke: sourceColor,
      };

      const markerEnd = {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
        color: sourceColor,
      };
      const id = nanoid(6);
      const edge = { id, source, target, type: 'deleteable', style, markerEnd, sourceHandle, targetHandle };
      set({ edges: [edge, ...get().edges] });
    },

    addDeviceNodeIfNotExist(data: TDeviceSchema) {
      if (get().nodes.find((node: Node) => node.data.sn === data.sn)) { return }
      let position = {x:  Math.random() * 800, y: Math.random() * 600 }
      const nodes = get().nodes.filter((node: Node) => node.type === 'device')
      if (nodes.length !==0 ) {
        // @ts-expect-error "omit"
        const bottomNode = nodes.sort((a, b) => b.position.y - a.position.y);
        position = { x: bottomNode[0].position.x, y: bottomNode[0].position.y + 100 }
      }

      const newNode: Node = {
        id: nanoid(),
        type: 'device',
        data: { ...data, sn: data.sn },
        position,
        targetPosition: Position.Left,
      }

      set({ nodes: [newNode, ...get().nodes] });
    },

    addNewNode(type: string) {
      let position = {x:  Math.random() * 800, y: Math.random() * 600 }
      // @ts-expect-error "omit"
      const nodes = get().nodes.filter((node) => node.type === type)
      if (nodes.length !==0 ) {
        // @ts-expect-error "omit"
        const bottomNode = nodes.sort((a, b) => b.position.y - a.position.y);
        position = {
          x: bottomNode[0].position.x,
          y: bottomNode[0].position.y + 100,
        }
      }

      const newNode: Node = {
        id: nanoid(),
        type: type,
        data: zodSchemaDefaultValues(schemaMap[type]) as Record<string, unknown>,
        position,
      };

      // @ts-expect-error "omit"
      if(nodeSourcePosition[type]) { newNode.sourcePosition = nodeSourcePosition[type]; }
      // @ts-expect-error "omit"
      if(nodeTargetPosition[type]) { newNode.targetPosition = nodeTargetPosition[type]; }

      set({ nodes: [newNode, ...get().nodes] })

      return newNode;
    },

    updateNodeData: (nodeId: string, data: TEntitySchemaUnion) => {
      set({
        nodes: get().nodes.map((node: Node) =>
          node.id === nodeId ? { ...node, data: { ...node.data, ...data } } : node
        ),
      });
    }
  }
}
