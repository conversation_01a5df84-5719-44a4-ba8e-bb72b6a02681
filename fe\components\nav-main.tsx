"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import { usePathname } from "next/navigation"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

import { cn } from "@/lib/utils"
import { ClientOnly } from "@/components/client-only"

type SidebarItem = {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: SidebarItem[]
  rightIcon?: LucideIcon
  rightIconClassName?: string
}

function NavMainContent({ items }: { items: SidebarItem[] }) {
  const pathname = usePathname()

  const isItemActive = (item: SidebarItem) => {
    if (item.items && item.items.length > 0) {
      return item.items.some(subItem => pathname === subItem.url)
    }
    return pathname === item.url
  }

  const renderItemWithChildren = function (item: SidebarItem) {
    const isActive = isItemActive(item)
    return (
      <Collapsible
        key={item.title}
        asChild
        defaultOpen={item.isActive}
        className="group/collapsible"
      >
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton
              tooltip={item.title}
              className={cn(
                "h-10 hover:!bg-secondary hover:!text-secondary-foreground",
                isActive && "font-semibold data-[active=true]:!bg-secondary data-[active=true]:!text-secondary-foreground"
              )}
              isActive={isActive}
            >
              {item.icon && <item.icon />}
              <span>{item.title}</span>
              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items?.map((subItem) => (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={pathname === subItem.url}
                    className={cn(
                      "hover:!bg-secondary hover:!text-secondary-foreground",
                      pathname === subItem.url && "font-semibold !bg-background !text-foreground"
                    )}
                  >
                    <a href={subItem.url}>
                      <span>{subItem.title}</span>
                    </a>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    )
  }

  function renderItemWithoutChildren(item: SidebarItem) {
    const isActive = isItemActive(item)

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          tooltip={item.title}
          className={cn(
            "h-10 hover:!bg-secondary hover:!text-secondary-foreground",
            isActive && "!font-semibold !bg-secondary !text-secondary-foreground"
          )}
          isActive={isActive}
        >
          {item.icon && <item.icon />}
          <a href={item.url}>
            <span>{item.title}</span>
          </a>
        { item.rightIcon && <item.rightIcon className={cn('ml-auto', item.rightIconClassName)} /> }
        </SidebarMenuButton>

      </SidebarMenuItem>
    )
  }

  return (
    <SidebarGroup>
      <SidebarMenu>

        {items.map((item) => {
          if (item.items && item.items.length > 0) {
            return renderItemWithChildren(item)
          }

          if (!item.items || item.items.length === 0) {
            return renderItemWithoutChildren(item)
          }
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

// 静态版本（服务端渲染用）
function NavMainStatic({ items }: { items: SidebarItem[] }) {
  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map((item) => {
          if (item.items && item.items.length > 0) {
            return (
              <Collapsible
                key={item.title}
                asChild
                defaultOpen={item.isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton tooltip={item.title} className="h-10 hover:!bg-secondary hover:!text-secondary-foreground">
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items?.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild className="hover:!bg-secondary hover:!text-secondary-foreground">
                            <a href={subItem.url}>
                              <span>{subItem.title}</span>
                            </a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            )
          }

          return (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton tooltip={item.title} className="h-10 hover:!bg-secondary hover:!text-secondary-foreground">
                {item.icon && <item.icon />}
                <a href={item.url}>
                  <span>{item.title}</span>
                </a>
                {item.rightIcon && <item.rightIcon className={cn('ml-auto', item.rightIconClassName)} />}
              </SidebarMenuButton>
            </SidebarMenuItem>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

// 主导出组件
export function NavMain({ items }: { items: SidebarItem[] }) {
  return (
    <ClientOnly fallback={<NavMainStatic items={items} />}>
      <NavMainContent items={items} />
    </ClientOnly>
  )
}
