"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import { usePathname } from "next/navigation"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

import { cn } from "@/lib/utils"

type SidebarItem = {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: SidebarItem[]
  rightIcon?: LucideIcon
  rightIconClassName?: string
}

export function NavMain({ items }: { items: SidebarItem[] }) {
  const pathname = usePathname()

  const isItemActive = (item: SidebarItem) => {
    if (item.items && item.items.length > 0) {
      return item.items.some(subItem => pathname === subItem.url)
    }
    return pathname === item.url
  }

  const renderItemWithChildren = function (item: SidebarItem) {
    const isActive = isItemActive(item)
    return (
      <Collapsible
        key={item.title}
        asChild
        defaultOpen={item.isActive}
        className="group/collapsible"
      >
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton
              tooltip={item.title}
              className={cn(
                "h-10 font-semibold",
                isActive && "!bg-secondary !text-secondary-foreground"
              )}
              isActive={isActive}
            >
              {item.icon && <item.icon />}
              <span>{item.title}</span>
              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items?.map((subItem) => (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={pathname === subItem.url}
                    className="font-semibold"
                  >
                    <a href={subItem.url}>
                      <span>{subItem.title}</span>
                    </a>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    )
  }

  function renderItemWithoutChildren(item: SidebarItem) {
    const isActive = isItemActive(item)

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          tooltip={item.title}
          className={cn(
            "h-10 font-semibold",
            isActive && "!bg-secondary !text-secondary-foreground"
          )}
          isActive={isActive}
        >
          {item.icon && <item.icon />}
          <a href={item.url}>
            <span>{item.title}</span>
          </a>
        { item.rightIcon && <item.rightIcon className={cn('ml-auto', item.rightIconClassName)} /> }
        </SidebarMenuButton>

      </SidebarMenuItem>
    )
  }

  return (
    <SidebarGroup>
      <SidebarMenu>

        {items.map((item) => {
          if (item.items && item.items.length > 0) {
            return renderItemWithChildren(item)
          }

          if (!item.items || item.items.length === 0) {
            return renderItemWithoutChildren(item)
          }
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
