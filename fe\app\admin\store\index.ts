import { createWithEqualityFn } from 'zustand/traditional';
import { persist, createJSONStorage } from 'zustand/middleware';
import { formula, type FormulaStore } from './formula';
import { scene, type SceneStore } from './scene';
import { dialogTracker, type DialogTrackerStore } from './dialogTracker';

type CombinedStore = FormulaStore & SceneStore & DialogTrackerStore;

export const useAppStore = createWithEqualityFn<CombinedStore>()(
  persist((set, get) => ({
    ...(formula(set, get)),
    ...(scene(set, get)),
    ...(dialogTracker(set))
  }), {
      name: 'formula-store',
      storage: createJSONStorage(() => localStorage)
    }
  ))

export {
  FormulaStore,
  SceneStore,
  DialogTrackerStore
}
