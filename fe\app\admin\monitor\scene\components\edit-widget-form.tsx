import React, { useCallback } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";

import { useAppStore } from '@/app/admin/store';

import { WidgetType } from '@/types';
import { StatSetting, LineSetting, BarSetting } from './widgets';

export type EditWidgetFormProps = {
  id: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export function EditWidgetForm({
  id,
  isOpen,
  setIsOpen,
}:EditWidgetFormProps) {
  const widget = useAppStore.getState().widgets.find((w) => w.id === id)
  const { openDialog, closeDialog } = useAppStore.getState();
  const widgetType = (widget?.type || "stat") as WidgetType

  const renderWidgetSetting = useCallback(
    () => {
        switch (widgetType) {
          case 'stat':
            return <StatSetting id={id} />
          case 'line':
            return <LineSetting id={id} />
          case 'bar':
            return <BarSetting id={id} />
        }

        return null;
    },  [widgetType, id]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (open) { openDialog(id); } else { closeDialog(id) }
        setIsOpen(open)
      }}
    >
      <DialogContent
      className='sm:max-w-3xl sm:max-h-[80vh] overflow-y-auto p-4'
      >
        <DialogHeader>
          <DialogTitle className='flex flex-row items-center'>
            修改图表
          </DialogTitle>
        </DialogHeader>

        { renderWidgetSetting() }
      </DialogContent>
    </Dialog>
  )
}
