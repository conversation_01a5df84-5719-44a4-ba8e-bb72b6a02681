{"name": "fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-hook/resize-observer": "^2.0.2", "@types/react-grid-layout": "^1.3.5", "@xyflow/react": "^12.8.2", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.532.0", "nanoid": "^5.1.5", "next": "15.4.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-colorful": "^5.6.1", "react-dom": "19.1.0", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.61.1", "react-resizable": "^3.0.5", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "usehooks-ts": "^3.1.1", "zod": "^4.0.15", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}